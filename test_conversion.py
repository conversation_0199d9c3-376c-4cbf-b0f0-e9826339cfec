#!/usr/bin/env python3

import sys
import json
import os

# Add the workflow-service to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'workflow-service'))

def test_tool_node_identification():
    """Test if tool nodes are properly identified"""
    print("🔍 Testing tool node identification...")
    
    try:
        # Load the agentic schema
        with open('agentic_schema.json', 'r') as f:
            agentic_schema = json.load(f)
        
        workflow_data = agentic_schema.get('workflow_data', {})
        nodes = workflow_data.get('nodes', [])
        edges = workflow_data.get('edges', [])
        
        print(f"📊 Loaded workflow data:")
        print(f"   - Nodes: {len(nodes)}")
        print(f"   - Edges: {len(edges)}")
        
        # Import the helper function
        from app.services.workflow_builder.workflow_schema_converter import is_tool_node
        
        # Check each node
        tool_nodes = []
        agent_nodes = []
        other_nodes = []
        
        for node in nodes:
            node_id = node.get('id', 'unknown')
            node_type = node.get('data', {}).get('type', 'unknown')
            original_type = node.get('data', {}).get('originalType', 'unknown')
            
            if is_tool_node(node, edges):
                tool_nodes.append((node_id, original_type))
            elif node_type == 'agent' or original_type == 'AgenticAI':
                agent_nodes.append((node_id, original_type))
            else:
                other_nodes.append((node_id, original_type))
        
        print(f"\n🔧 Tool nodes (should be integrated into agents):")
        for node_id, node_type in tool_nodes:
            print(f"   - {node_id} ({node_type})")
        
        print(f"\n🤖 Agent nodes:")
        for node_id, node_type in agent_nodes:
            print(f"   - {node_id} ({node_type})")
        
        print(f"\n📦 Other nodes:")
        for node_id, node_type in other_nodes:
            print(f"   - {node_id} ({node_type})")
        
        # Check tool connections
        tool_edges = [e for e in edges if e.get('targetHandle') == 'tools']
        print(f"\n🔗 Tool connections:")
        for edge in tool_edges:
            source = edge.get('source')
            target = edge.get('target')
            print(f"   - {source} -> {target} (tools handle)")
        
        return len(tool_nodes), len(agent_nodes), len(other_nodes)
        
    except Exception as e:
        print(f"❌ Error in tool node identification: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0, 0

def test_schema_conversion():
    """Test the full schema conversion"""
    print("\n🔄 Testing schema conversion...")
    
    try:
        # Load the agentic schema
        with open('agentic_schema.json', 'r') as f:
            agentic_schema = json.load(f)
        
        workflow_data = agentic_schema.get('workflow_data', {})
        
        # Import the conversion function
        from app.services.workflow_builder.workflow_schema_converter import convert_workflow_to_transition_schema
        
        # Convert to transition schema
        result = convert_workflow_to_transition_schema(workflow_data)
        
        print(f"\n🎉 CONVERSION SUCCESSFUL!")
        print(f"   - Nodes in result: {len(result['nodes'])}")
        print(f"   - Transitions in result: {len(result['transitions'])}")
        
        # Check transition IDs
        transition_ids = [t['id'] for t in result['transitions']]
        print(f"\n📋 Transition IDs:")
        for tid in transition_ids:
            print(f"   - {tid}")
        
        # Check if any tool nodes became transitions (they shouldn't)
        tool_transition_count = 0
        for tid in transition_ids:
            if 'MCP_' in tid and 'AgenticAI' not in tid:
                tool_transition_count += 1
                print(f"   ⚠️  Tool node became transition: {tid}")
        
        if tool_transition_count == 0:
            print(f"   ✅ No tool nodes became separate transitions!")
        else:
            print(f"   ❌ {tool_transition_count} tool nodes became separate transitions")
        
        return True, len(result['transitions']), tool_transition_count
        
    except Exception as e:
        print(f"❌ CONVERSION FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False, 0, 0

if __name__ == "__main__":
    print("🚀 Starting tool node conversion test...")
    print("=" * 60)
    
    # Test 1: Tool node identification
    tool_count, agent_count, other_count = test_tool_node_identification()
    
    # Test 2: Schema conversion
    success, transition_count, tool_transition_count = test_schema_conversion()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY:")
    print(f"   - Tool nodes identified: {tool_count}")
    print(f"   - Agent nodes identified: {agent_count}")
    print(f"   - Other nodes identified: {other_count}")
    print(f"   - Conversion successful: {success}")
    print(f"   - Total transitions created: {transition_count}")
    print(f"   - Tool nodes that became transitions: {tool_transition_count}")
    
    if success and tool_transition_count == 0:
        print("\n🎉 ALL TESTS PASSED! Tool nodes are properly integrated into agents.")
    else:
        print("\n❌ TESTS FAILED! Tool nodes are still being created as separate transitions.")
