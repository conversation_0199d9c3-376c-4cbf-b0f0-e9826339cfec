# Credential Management System Implementation Plan

## Executive Summary

This document outlines the comprehensive implementation plan to replace the current mock credential system with proper API integration in the workflow-builder-app. The current system uses localStorage-based mock implementations that need to be replaced with authenticated API calls to the existing credential endpoints in the api-gateway.

## Current State Analysis

### Frontend Mock Implementation
- **Location**: `workflow-builder-app/src/lib/mock-credential-service.ts`
- **Storage**: localStorage with keys `workflow_builder_credentials` and `${STORAGE_KEY}_${id}_value`
- **Functions**: `mockFetchCredentials()`, `mockCreateCredential()`, `mockDeleteCredential()`, `mockGetCredentialValue()`
- **Usage**: Currently imported and used in `api.ts` and `CredentialManager.tsx`

### API Gateway Implementation
- **Location**: `api-gateway/app/api/routers/credential_routes.py`
- **Endpoints Available**:
  - `POST /credentials` - Create credential
  - `GET /credentials` - List credentials
  - `GET /credentials/{credential_id}` - Get specific credential
  - `PUT /credentials/{credential_id}` - Update credential
  - `DELETE /credentials/{credential_id}` - Delete credential
- **Authentication**: Role-based authentication with `role_required(["user"])`
- **Backend Service**: Uses `UserServiceClient()` for gRPC communication

### Schema Mismatches Identified

#### Frontend Schema (Mock)
```typescript
interface Credential {
  id: string;
  name: string;
  type: string;
}

interface CredentialCreate {
  name: string;
  type: string;
  value: string;
}
```

#### Backend Schema (API Gateway)
```python
class CredentialCreate(BaseModel):
    key_name: str  # ← Different field name
    value: str
    description: Optional[str] = None  # ← Additional field

class CredentialInfo(BaseModel):
    id: str
    key_name: str  # ← Different field name
    description: Optional[str]
    value: str  # ← Includes value in response
    created_at: str
    updated_at: str
    last_used_at: str
```

## Implementation Plan

### Phase 1: Schema Alignment and Type Definitions

#### 1.1 Update Frontend Types
- **File**: `workflow-builder-app/src/lib/api.ts`
- **Action**: Update interfaces to match backend schema
- **Changes**:
  - Rename `name` to `key_name` in `CredentialCreate`
  - Add `description` field to `CredentialCreate`
  - Update `Credential` interface to include all backend fields
  - Add proper response wrapper types

#### 1.2 Create Unified Type Definitions
- **File**: `workflow-builder-app/src/types/credentials.ts`
- **Action**: Create centralized type definitions
- **Include**: Request/response types, error types, validation schemas

### Phase 2: Authentication Integration

#### 2.1 Update API Configuration
- **File**: `workflow-builder-app/src/lib/apiConfig.ts`
- **Action**: Add credential endpoints to API_ENDPOINTS
- **Endpoints**:
  ```typescript
  CREDENTIALS: {
    LIST: `${API_BASE_URL}/credentials`,
    CREATE: `${API_BASE_URL}/credentials`,
    GET: (id: string) => `${API_BASE_URL}/credentials/${id}`,
    UPDATE: (id: string) => `${API_BASE_URL}/credentials/${id}`,
    DELETE: (id: string) => `${API_BASE_URL}/credentials/${id}`,
  }
  ```

#### 2.2 Create Authenticated Credential Service
- **File**: `workflow-builder-app/src/services/credentialService.ts`
- **Action**: Implement authenticated API calls using centralized axios instance
- **Features**: Error handling, type safety, request/response transformation

### Phase 3: Core API Implementation

#### 3.1 Replace Mock Functions
- **File**: `workflow-builder-app/src/lib/api.ts`
- **Action**: Replace mock implementations with real API calls
- **Functions**: `fetchCredentials()`, `createCredential()`, `deleteCredential()`
- **Requirements**: Maintain backward compatibility, add proper error handling

#### 3.2 Add Missing Functionality
- **New Functions**:
  - `getCredential(id: string)` - Get specific credential
  - `updateCredential(id: string, data: CredentialUpdate)` - Update credential
  - `getCredentialValue(id: string)` - Secure value retrieval for workflow execution

### Phase 4: UI Component Updates

#### 4.1 Update CredentialManager Component
- **File**: `workflow-builder-app/src/components/credentials/CredentialManager.tsx`
- **Changes**:
  - Update form to include `description` field
  - Handle new response format
  - Add update functionality
  - Improve error handling and loading states

#### 4.2 Update Inspector Panel Integration
- **File**: `workflow-builder-app/src/components/inspector/InputRenderer.tsx`
- **Changes**:
  - Replace placeholder credential selection with real dropdown
  - Integrate with credential API for fetching available credentials
  - Add credential type filtering based on component requirements

### Phase 5: Workflow Integration

#### 5.1 Credential Selection Component
- **File**: `workflow-builder-app/src/components/credentials/CredentialSelector.tsx`
- **Action**: Create reusable credential selection component
- **Features**: 
  - Dropdown with search/filter
  - Type-based filtering
  - Real-time credential loading
  - Error handling

#### 5.2 Update Workflow Execution
- **Files**: Workflow execution related components
- **Action**: Integrate credential value retrieval for workflow execution
- **Security**: Ensure credential values are only retrieved when needed for execution

### Phase 6: Testing and Validation

#### 6.1 Unit Tests
- **Coverage Target**: 95%+
- **Test Files**:
  - `credentialService.test.ts`
  - `CredentialManager.test.tsx`
  - `CredentialSelector.test.tsx`
- **Test Cases**: API calls, error handling, UI interactions, authentication

#### 6.2 Integration Tests
- **Scope**: End-to-end credential management workflows
- **Scenarios**: Create, read, update, delete, workflow integration

#### 6.3 Performance Benchmarks
- **Targets**: 
  - API response time < 100ms
  - UI rendering < 50ms
  - Credential loading < 200ms

## Technical Specifications

### Error Handling Strategy
1. **Network Errors**: Retry logic with exponential backoff
2. **Authentication Errors**: Automatic token refresh and re-authentication
3. **Validation Errors**: User-friendly error messages with field-specific feedback
4. **Server Errors**: Graceful degradation with fallback options

### Security Considerations
1. **Credential Values**: Never store in frontend state longer than necessary
2. **API Calls**: All requests authenticated with Bearer tokens
3. **Error Messages**: Sanitize error responses to prevent information leakage
4. **Caching**: Implement secure caching strategy for credential metadata only

### Performance Optimizations
1. **Lazy Loading**: Load credentials only when needed
2. **Caching**: Cache credential lists with appropriate TTL
3. **Debouncing**: Implement search debouncing for credential selection
4. **Pagination**: Support pagination for large credential lists

## Migration Strategy

### Phase 1: Preparation (Week 1)
- Update type definitions
- Create new service layer
- Set up testing framework

### Phase 2: Core Implementation (Week 2)
- Implement authenticated API calls
- Replace mock functions
- Update core components

### Phase 3: UI Enhancement (Week 3)
- Update CredentialManager
- Implement CredentialSelector
- Integrate with workflow components

### Phase 4: Testing & Deployment (Week 4)
- Comprehensive testing
- Performance optimization
- Production deployment

## Risk Assessment

### High Risk
- **Authentication Issues**: Potential token refresh problems during credential operations
- **Schema Mismatches**: Backend/frontend schema inconsistencies causing runtime errors

### Medium Risk
- **Performance Impact**: Increased API calls may affect application performance
- **User Experience**: Transition from localStorage to API may introduce loading delays

### Low Risk
- **Backward Compatibility**: Well-defined interfaces minimize breaking changes
- **Testing Coverage**: Comprehensive test suite reduces regression risk

## Success Criteria

1. **Functional**: All credential operations work with real API
2. **Performance**: No degradation in user experience
3. **Security**: Proper authentication and secure credential handling
4. **Maintainability**: Clean, well-documented code with high test coverage
5. **User Experience**: Seamless transition with improved functionality

## Detailed Implementation Specifications

### API Endpoint Mapping

#### Current Mock Functions → API Endpoints
```typescript
// Mock Function → API Endpoint → HTTP Method
mockFetchCredentials() → /api/credentials → GET
mockCreateCredential() → /api/credentials → POST
mockDeleteCredential() → /api/credentials/{id} → DELETE
// New functions needed:
getCredential() → /api/credentials/{id} → GET
updateCredential() → /api/credentials/{id} → PUT
```

#### Request/Response Transformation
```typescript
// Frontend Request → Backend Request
{
  name: string,           → key_name: string,
  type: string,           → (removed - not in backend)
  value: string           → value: string,
                          → description?: string (new)
}

// Backend Response → Frontend Response
{
  id: string,             → id: string,
  key_name: string,       → name: string (mapped),
  description?: string,   → description?: string,
  value: string,          → (exclude from list view),
  created_at: string,     → createdAt: string (mapped),
  updated_at: string,     → updatedAt: string (mapped),
  last_used_at: string    → lastUsedAt: string (mapped)
}
```

### Reusable Utility Functions

#### 1. Credential Service Layer
```typescript
// File: src/services/credentialService.ts
class CredentialService {
  private api: AxiosInstance;

  async fetchCredentials(): Promise<CredentialListResponse>
  async createCredential(data: CredentialCreateRequest): Promise<Credential>
  async getCredential(id: string): Promise<Credential>
  async updateCredential(id: string, data: CredentialUpdateRequest): Promise<Credential>
  async deleteCredential(id: string): Promise<void>
  async getCredentialValue(id: string): Promise<string> // For workflow execution
}
```

#### 2. Data Transformation Utilities
```typescript
// File: src/utils/credentialTransforms.ts
export const transformCredentialForBackend = (frontend: CredentialCreate): BackendCredentialCreate
export const transformCredentialFromBackend = (backend: BackendCredential): Credential
export const transformCredentialListFromBackend = (backend: BackendCredentialList): CredentialList
```

#### 3. Validation Utilities
```typescript
// File: src/utils/credentialValidation.ts
export const validateCredentialCreate = (data: CredentialCreate): ValidationResult
export const validateCredentialUpdate = (data: CredentialUpdate): ValidationResult
export const sanitizeCredentialValue = (value: string): string
```

### Component Architecture

#### 1. CredentialProvider Context
```typescript
// File: src/contexts/CredentialContext.tsx
interface CredentialContextType {
  credentials: Credential[];
  loading: boolean;
  error: string | null;
  fetchCredentials: () => Promise<void>;
  createCredential: (data: CredentialCreate) => Promise<Credential>;
  updateCredential: (id: string, data: CredentialUpdate) => Promise<Credential>;
  deleteCredential: (id: string) => Promise<void>;
  refreshCredentials: () => Promise<void>;
}
```

#### 2. Custom Hooks
```typescript
// File: src/hooks/useCredentials.ts
export const useCredentials = () => {
  // Returns credential context with loading states
}

// File: src/hooks/useCredentialSelector.ts
export const useCredentialSelector = (credentialType?: string) => {
  // Returns filtered credentials for selection
}
```

### Error Handling Framework

#### 1. Error Types
```typescript
// File: src/types/errors.ts
export enum CredentialErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  SERVER_ERROR = 'SERVER_ERROR'
}

export interface CredentialError {
  type: CredentialErrorType;
  message: string;
  field?: string;
  code?: string;
}
```

#### 2. Error Handler Utility
```typescript
// File: src/utils/credentialErrorHandler.ts
export const handleCredentialError = (error: any): CredentialError
export const getErrorMessage = (error: CredentialError): string
export const shouldRetry = (error: CredentialError): boolean
```

### Testing Strategy

#### 1. Test Data Factories
```typescript
// File: src/test/factories/credentialFactory.ts
export const createMockCredential = (overrides?: Partial<Credential>): Credential
export const createMockCredentialCreate = (overrides?: Partial<CredentialCreate>): CredentialCreate
export const createMockCredentialResponse = (overrides?: Partial<CredentialResponse>): CredentialResponse
```

#### 2. API Mocking Strategy
```typescript
// File: src/test/mocks/credentialApiMocks.ts
export const mockCredentialApi = {
  fetchCredentials: jest.fn(),
  createCredential: jest.fn(),
  getCredential: jest.fn(),
  updateCredential: jest.fn(),
  deleteCredential: jest.fn()
}
```

#### 3. Test Coverage Requirements
- **Unit Tests**: 95%+ coverage for all service functions
- **Integration Tests**: All API endpoints with authentication
- **Component Tests**: All UI interactions and error states
- **E2E Tests**: Complete credential management workflows

### Performance Optimization

#### 1. Caching Strategy
```typescript
// File: src/utils/credentialCache.ts
interface CacheConfig {
  ttl: number; // Time to live in milliseconds
  maxSize: number; // Maximum cache entries
}

class CredentialCache {
  private cache: Map<string, CachedCredential>;
  private config: CacheConfig;

  get(key: string): Credential | null
  set(key: string, credential: Credential): void
  invalidate(key?: string): void
  clear(): void
}
```

#### 2. Request Optimization
- **Debouncing**: 300ms delay for search inputs
- **Pagination**: 50 items per page for credential lists
- **Lazy Loading**: Load credentials only when credential selector is opened
- **Request Deduplication**: Prevent duplicate API calls

### Security Implementation

#### 1. Credential Value Handling
```typescript
// File: src/utils/secureCredentialHandler.ts
class SecureCredentialHandler {
  // Never store credential values in component state
  async getCredentialValueForExecution(id: string): Promise<string>

  // Clear sensitive data from memory
  clearSensitiveData(): void

  // Validate credential access permissions
  validateCredentialAccess(credentialId: string, userId: string): boolean
}
```

#### 2. API Security
- **Request Sanitization**: Sanitize all input data before API calls
- **Response Filtering**: Filter sensitive data from API responses
- **Token Validation**: Validate authentication tokens before credential operations
- **Rate Limiting**: Implement client-side rate limiting for credential operations

## Next Steps

1. **Immediate**: Begin Phase 1 implementation with schema alignment
2. **Week 1**: Complete type definitions and service layer setup
3. **Week 2**: Implement core API integration and testing
4. **Week 3**: UI updates and workflow integration
5. **Week 4**: Final testing and deployment preparation

This implementation plan provides a structured approach to replacing the mock credential system with a robust, secure, and maintainable API-integrated solution.
