# Tool Nodes as Source of Truth Solution

## 🎯 Core Concept

Instead of storing tools only in AgenticAI's config, we store them as **actual workflow nodes** and dynamically generate the `config.tool_connections` during save/conversion operations.

## 📊 Current Problem vs Solution

### ❌ Current Problem (agentic_schema.json)
```json
{
  "workflow_data": {
    "nodes": [
      {"id": "start-node", ...},
      {
        "id": "AgenticAI-123", 
        "data": {
          "config": {
            "tool_connections": {
              "tool_1": [
                {"node_id": "tool-1", "component_definition": {...}},
                {"node_id": "tool-2", "component_definition": {...}},
                {"node_id": "tool-3", "component_definition": {...}},
                {"node_id": "tool-4", "component_definition": {...}}
              ]
            }
          }
        }
      }
    ],
    "edges": [{"source": "start-node", "target": "AgenticAI-123"}]
  }
}
```

**Issues:**
- Tools exist only as config data (invisible on canvas)
- No individual tool management
- Hard to visualize tool relationships
- Tools can't be configured independently

### ✅ Proposed Solution
```json
{
  "workflow_data": {
    "nodes": [
      {"id": "start-node", ...},
      {"id": "AgenticAI-123", "data": {"config": {}}},
      {
        "id": "tool-candidate-suitability",
        "type": "WorkflowNode",
        "data": {
          "originalType": "MCP_Candidate_Interview_candidate_suitability",
          "label": "Candidate Interview - candidate_suitability",
          "definition": {
            "name": "MCP_Candidate_Interview_candidate_suitability",
            "mcp_info": {...}
          }
        }
      },
      {
        "id": "tool-tavily-crawl",
        "type": "WorkflowNode", 
        "data": {
          "originalType": "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-crawl",
          "label": "Tavily Web Search and Extraction Server - tavily-crawl",
          "definition": {...}
        }
      },
      {
        "id": "tool-calendar-update",
        "type": "WorkflowNode",
        "data": {
          "originalType": "MCP_google-calendar-mcp_update_event",
          "label": "google-calendar-mcp - update_event",
          "definition": {...}
        }
      },
      {
        "id": "tool-fetch",
        "type": "WorkflowNode",
        "data": {
          "originalType": "MCP_mcp-fetch_fetch",
          "label": "mcp-fetch - fetch", 
          "definition": {...}
        }
      }
    ],
    "edges": [
      {"source": "start-node", "target": "AgenticAI-123"},
      {"source": "tool-candidate-suitability", "target": "AgenticAI-123", "targetHandle": "tools"},
      {"source": "tool-tavily-crawl", "target": "AgenticAI-123", "targetHandle": "tools"},
      {"source": "tool-calendar-update", "target": "AgenticAI-123", "targetHandle": "tools"},
      {"source": "tool-fetch", "target": "AgenticAI-123", "targetHandle": "tools"}
    ]
  }
}
```

## 🔄 Dynamic Config Generation Flow

### Phase 1: Canvas Interaction (Frontend)
```
User Action: Drag MCP tool from marketplace
    ↓
Create actual workflow node with full definition
    ↓
User connects tool to AgenticAI via edge
    ↓
Edge stored: {source: "tool-id", target: "AgenticAI-id", targetHandle: "tools"}
```

### Phase 2: Workflow Save (Frontend)
```typescript
// Before saving workflow, generate AgenticAI configs from connected tools
function generateToolConnectionsFromEdges(agenticNodeId: string, edges: Edge[], nodes: Node[]) {
  const toolConnections = {};
  
  // Find all tools connected to this AgenticAI node
  const connectedToolEdges = edges.filter(edge => 
    edge.target === agenticNodeId && 
    edge.targetHandle === "tools"
  );
  
  const connectedTools = connectedToolEdges.map(edge => {
    const toolNode = nodes.find(n => n.id === edge.source);
    return {
      node_id: toolNode.id,
      node_type: toolNode.data.originalType,
      node_label: toolNode.data.label,
      component_definition: toolNode.data.definition
    };
  });
  
  // Store all tools under "tools" handle (single handle approach)
  toolConnections["tools"] = connectedTools;
  
  return toolConnections;
}

// Apply to all AgenticAI nodes before save
function prepareWorkflowForSave(workflowData) {
  const { nodes, edges } = workflowData;
  
  nodes.forEach(node => {
    if (node.data.originalType === "AgenticAI") {
      node.data.config.tool_connections = generateToolConnectionsFromEdges(
        node.id, edges, nodes
      );
    }
  });
  
  return workflowData;
}
```

### Phase 3: Workflow Load (Frontend)
```typescript
// When loading saved workflow, tools already exist as nodes
// No special handling needed - canvas renders all nodes normally
function loadWorkflow(savedWorkflowData) {
  // Tools appear as individual nodes on canvas
  // Edges show connections to AgenticAI
  // Users can see, move, and configure each tool independently
  return savedWorkflowData;
}
```

### Phase 4: Transition Schema Conversion (Backend)
```python
def convert_workflow_to_transition_schema(workflow_data):
    nodes = workflow_data.get("nodes", [])
    edges = workflow_data.get("edges", [])
    
    # Tools are already visible as individual nodes
    # No need for virtual node extraction
    
    transitions = []
    
    for node in nodes:
        if node.get("data", {}).get("originalType") == "AgenticAI":
            # AgenticAI transition with tool references
            transitions.append({
                "id": f"transition-{node['id']}",
                "type": "agent",
                "config": node["data"]["config"],  # Contains tool_connections
                "dependencies": get_dependencies(node["id"], edges)
            })
        elif node.get("data", {}).get("originalType", "").startswith("MCP_"):
            # Individual tool transitions
            transitions.append({
                "id": f"transition-{node['id']}",
                "type": "mcp_tool",
                "config": {
                    "tool_name": node["data"]["definition"]["mcp_info"]["tool_name"],
                    "server_id": node["data"]["definition"]["mcp_info"]["server_id"],
                    "input_schema": node["data"]["definition"]["mcp_info"]["input_schema"]
                },
                "dependencies": get_dependencies(node["id"], edges)
            })
    
    return {"transitions": transitions}
```

## 🎨 User Experience Benefits

### ✅ **Visual Canvas Management**
- **Individual Tool Nodes**: Each tool appears as a separate, draggable node
- **Clear Connections**: Visual edges show tool-to-agent relationships  
- **Tool Organization**: Users can arrange tools spatially for better workflow understanding
- **Independent Configuration**: Click any tool to configure its specific parameters

### ✅ **Workflow Building**
```
1. User drags "Candidate Suitability" tool from MCP marketplace
   → Creates tool node on canvas

2. User drags "Web Crawler" tool from MCP marketplace  
   → Creates another tool node on canvas

3. User connects both tools to AgenticAI node
   → Creates visual edges, tools become available to agent

4. User can click each tool individually to configure parameters
   → Each tool has its own configuration panel
```

### ✅ **Workflow Management**
- **Tool Reuse**: Same tool can be connected to multiple agents
- **Tool Removal**: Delete tool node or disconnect edge to remove from agent
- **Tool Updates**: Update tool definition affects all connected agents
- **Visual Debugging**: See exactly which tools are connected to which agents

## 🔧 Implementation Benefits

### ✅ **Data Consistency**
- **Single Source of Truth**: Tool definition exists in one place (the tool node)
- **No Duplication**: Tool data not duplicated in AgenticAI config
- **Automatic Sync**: Config generated from actual connections, always in sync
- **Version Control**: Clear, readable workflow structure in saved files

### ✅ **Execution Benefits**
- **Separate Transitions**: Each tool becomes its own executable transition
- **Parallel Execution**: Tools can be executed independently and in parallel
- **Dependency Management**: Clear dependencies between agent and tools
- **Error Isolation**: Tool failures don't affect other tools

### ✅ **Development Benefits**
- **Easier Testing**: Test individual tools separately
- **Modular Architecture**: Tools are self-contained components
- **Scalability**: Add new tools without modifying AgenticAI logic
- **Maintainability**: Tool updates isolated to tool definitions

## 🚀 Migration Strategy

### Step 1: Enhance Frontend
```typescript
// Add tool node creation from MCP marketplace
// Modify save logic to generate tool_connections from edges
// Update load logic to handle tool nodes
```

### Step 2: Update Backend
```python
# Modify workflow converter to handle tool nodes as separate transitions
# Update AgenticAI execution to reference tool transitions
# Ensure backward compatibility with existing config-based workflows
```

### Step 3: Gradual Migration
```
1. New workflows use tool nodes approach
2. Existing workflows continue to work with config-based tools
3. Optional migration tool to convert old workflows to new format
4. Eventually deprecate config-based approach
```

## 🎯 End Result

### Before (Current)
```
Canvas: [Start] → [AgenticAI (with 4 hidden tools)]
Execution: AgenticAI handles tools internally
```

### After (Proposed)
```
Canvas: [Start] → [AgenticAI] ← [Tool1] [Tool2] [Tool3] [Tool4]
Execution: AgenticAI → Tool1 → AgenticAI → Tool2 → AgenticAI → etc.
```

This approach provides the best of both worlds: excellent user experience with visual tool management, and robust execution with proper tool isolation and dependency management.