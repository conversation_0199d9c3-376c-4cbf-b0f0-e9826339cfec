# Credential Management Technical Requirements

## API Endpoint Completeness Analysis

### ✅ Available Endpoints (api-gateway)

| Endpoint | Method | Purpose | Status | Notes |
|----------|--------|---------|---------|-------|
| `/credentials` | POST | Create credential | ✅ Complete | Includes validation, auth |
| `/credentials` | GET | List user credentials | ✅ Complete | User-scoped, paginated |
| `/credentials/{id}` | GET | Get specific credential | ✅ Complete | Includes full details |
| `/credentials/{id}` | PUT | Update credential | ✅ Complete | Partial updates supported |
| `/credentials/{id}` | DELETE | Delete credential | ✅ Complete | User-scoped deletion |

### ❌ Missing Endpoints

| Endpoint | Method | Purpose | Priority | Implementation Needed |
|----------|--------|---------|----------|----------------------|
| `/credentials/{id}/value` | GET | Get credential value only | **HIGH** | Secure value retrieval for workflow execution |
| `/credentials/types` | GET | List supported credential types | **MEDIUM** | For UI dropdown population |
| `/credentials/validate` | POST | Validate credential format | **LOW** | Client-side validation sufficient |

## Frontend Implementation Gaps

### 1. Core API Integration

#### Missing Service Layer
```typescript
// File: src/services/credentialService.ts - NEEDS IMPLEMENTATION
interface CredentialServiceInterface {
  // ❌ Not implemented - using mocks
  fetchCredentials(): Promise<CredentialListResponse>
  createCredential(data: CredentialCreateRequest): Promise<Credential>
  getCredential(id: string): Promise<Credential>
  updateCredential(id: string, data: CredentialUpdateRequest): Promise<Credential>
  deleteCredential(id: string): Promise<void>
  
  // ❌ Critical missing function for workflow execution
  getCredentialValueForExecution(id: string): Promise<string>
}
```

#### Missing Type Definitions
```typescript
// File: src/types/credentials.ts - NEEDS CREATION
interface BackendCredentialCreate {
  key_name: string;      // ❌ Frontend uses 'name'
  value: string;
  description?: string;  // ❌ Not in frontend interface
}

interface BackendCredentialResponse {
  success: boolean;      // ❌ Not handled in frontend
  message: string;       // ❌ Not handled in frontend
  credentials: BackendCredentialInfo[];
}

interface BackendCredentialInfo {
  id: string;
  key_name: string;      // ❌ Frontend expects 'name'
  description?: string;
  value: string;         // ❌ Security concern - should be excluded from lists
  created_at: string;    // ❌ Not in frontend interface
  updated_at: string;    // ❌ Not in frontend interface
  last_used_at: string;  // ❌ Not in frontend interface
}
```

### 2. UI Component Gaps

#### CredentialManager Component Issues
**File**: `src/components/credentials/CredentialManager.tsx`

```typescript
// ❌ Missing fields in form
interface CredentialFormData {
  name: string;          // Should be key_name for backend
  type: string;          // Not used by backend
  value: string;
  // Missing: description field
}

// ❌ Missing functionality
// - Update credential capability
// - Description field in form
// - Proper error handling for API failures
// - Loading states for individual operations
// - Credential type management
```

#### Missing Credential Selector Component
**File**: `src/components/credentials/CredentialSelector.tsx` - **NEEDS CREATION**

```typescript
interface CredentialSelectorProps {
  value?: string;                    // Selected credential ID
  onChange: (credentialId: string) => void;
  credentialType?: string;           // Filter by type
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
  onCreateNew?: () => void;         // Quick create option
}

// Required features:
// - Real-time credential loading
// - Search/filter functionality
// - Type-based filtering
// - Loading states
// - Error handling
// - Create new credential option
```

#### Inspector Panel Integration Issues
**File**: `src/components/inspector/InputRenderer.tsx`

```typescript
// ❌ Current implementation shows placeholder
case "credential":
  return (
    <div className="mt-1 space-y-2">
      {value?.use_credential_id ? (
        <p className="text-muted-foreground text-xs">
          Credential selection UI would go here  // ❌ Not implemented
        </p>
      ) : (
        // Direct input works but no credential selector
      )}
    </div>
  );

// ✅ Required implementation:
// - Real credential dropdown
// - Integration with CredentialSelector component
// - Proper credential loading and error handling
```

### 3. Authentication Integration Gaps

#### API Configuration Missing
**File**: `src/lib/apiConfig.ts`

```typescript
// ❌ Missing credential endpoints
export const API_ENDPOINTS = {
  // ... existing endpoints
  
  // NEEDS ADDITION:
  CREDENTIALS: {
    LIST: `${API_BASE_URL}/credentials`,
    CREATE: `${API_BASE_URL}/credentials`,
    GET: (id: string) => `${API_BASE_URL}/credentials/${id}`,
    UPDATE: (id: string) => `${API_BASE_URL}/credentials/${id}`,
    DELETE: (id: string) => `${API_BASE_URL}/credentials/${id}`,
    GET_VALUE: (id: string) => `${API_BASE_URL}/credentials/${id}/value`, // If implemented
  }
};
```

#### Authenticated API Client Issues
**File**: `src/lib/authenticatedApi.ts`

```typescript
// ✅ Basic structure exists but not used
export const authenticatedApi = {
  fetchCredentials: async (): Promise<CredentialListResponse> => {
    // ✅ Implemented but not used by main API functions
  },
  
  // ❌ Missing functions:
  // - getCredential(id: string)
  // - updateCredential(id: string, data: CredentialUpdate)
  // - getCredentialValue(id: string) - for workflow execution
};
```

## Backend API Improvements Needed

### 1. Security Enhancements

#### Credential Value Exposure Issue
**File**: `api-gateway/app/api/routers/credential_routes.py`

```python
# ❌ SECURITY ISSUE: Values included in list response
@credential_router.get("", response_model=CredentialListResponse)
async def list_credentials(current_user: dict = Depends(role_required(["user"]))):
    # Current implementation includes credential values in list
    # Should exclude values for security
    
    credential_list.append(
        CredentialInfo(
            id=cred_dict.get("id"),
            key_name=cred_dict.get("key_name"),
            description=cred_dict.get("description"),
            value=cred_dict.get("value"),  # ❌ Should be excluded from list
            # ... timestamps
        )
    )
```

#### Recommended Backend Changes
```python
# ✅ RECOMMENDED: Separate schemas for list vs detail
class CredentialListItem(BaseModel):
    id: str
    key_name: str
    description: Optional[str]
    created_at: str
    updated_at: str
    last_used_at: str
    # value excluded for security

class CredentialDetails(BaseModel):
    id: str
    key_name: str
    description: Optional[str]
    value: str  # Only in detail view
    created_at: str
    updated_at: str
    last_used_at: str

# ✅ RECOMMENDED: Separate endpoint for value retrieval
@credential_router.get("/{credential_id}/value")
async def get_credential_value(credential_id: str, current_user: dict):
    # Secure value retrieval for workflow execution
    # Additional validation for execution context
```

### 2. Missing Backend Endpoints

#### Credential Types Endpoint
```python
# ❌ MISSING: Credential types endpoint
@credential_router.get("/types", response_model=CredentialTypesResponse)
async def get_credential_types():
    # Return supported credential types for UI
    return {
        "types": [
            {"id": "api_key", "name": "API Key", "description": "..."},
            {"id": "oauth_token", "name": "OAuth Token", "description": "..."},
            {"id": "password", "name": "Password", "description": "..."},
            # ...
        ]
    }
```

## Workflow Integration Requirements

### 1. Secure Credential Value Retrieval

#### Workflow Execution Context
```typescript
// File: src/services/workflowExecutionService.ts - NEEDS ENHANCEMENT
interface WorkflowExecutionContext {
  credentialValues: Map<string, string>;  // ❌ Should not store values
  
  // ✅ RECOMMENDED: Secure retrieval function
  getCredentialValue(credentialId: string): Promise<string>;
}

// ✅ Required implementation:
class SecureCredentialHandler {
  async getCredentialValueForExecution(
    credentialId: string,
    executionContext: string
  ): Promise<string> {
    // Validate execution context
    // Retrieve value securely
    // Log access for audit
    // Clear from memory after use
  }
}
```

### 2. Component Integration

#### Credential Input Component Enhancement
```typescript
// File: src/components/inspector/inputs/CredentialInput.tsx - NEEDS CREATION
interface CredentialInputProps {
  inputDef: CredentialInputDefinition;
  value: CredentialInputValue;
  onChange: (name: string, value: CredentialInputValue) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId: string;
  availableCredentials: Credential[];  // From context
}

interface CredentialInputValue {
  use_credential_id: boolean;
  credential_id?: string;
  value?: string;  // Direct input when not using stored credential
  credential_type?: string;  // For filtering
}
```

## Testing Requirements

### 1. Unit Test Coverage Gaps

#### Missing Test Files
- ❌ `src/services/credentialService.test.ts`
- ❌ `src/components/credentials/CredentialSelector.test.tsx`
- ❌ `src/components/credentials/CredentialManager.test.tsx` (needs API integration tests)
- ❌ `src/utils/credentialTransforms.test.ts`
- ❌ `src/hooks/useCredentials.test.ts`

#### Required Test Scenarios
```typescript
// API Integration Tests
describe('CredentialService', () => {
  test('handles authentication errors gracefully')
  test('transforms request/response data correctly')
  test('implements proper retry logic')
  test('caches credentials appropriately')
  test('handles network failures')
})

// Component Integration Tests  
describe('CredentialManager', () => {
  test('creates credentials with description field')
  test('updates existing credentials')
  test('handles API errors with user-friendly messages')
  test('validates form input properly')
})

// Workflow Integration Tests
describe('Credential Workflow Integration', () => {
  test('retrieves credential values securely for execution')
  test('filters credentials by type in component configuration')
  test('handles credential access errors during execution')
})
```

### 2. Performance Testing Requirements

#### Load Testing Scenarios
- ✅ Credential list loading with 100+ credentials
- ✅ Concurrent credential operations
- ✅ Search/filter performance with large datasets
- ✅ Cache invalidation and refresh performance

#### Performance Benchmarks
- ✅ Credential list loading: < 200ms
- ✅ Credential creation: < 500ms
- ✅ Credential search: < 100ms
- ✅ UI rendering: < 50ms

## Implementation Priority Matrix

### Phase 1: Critical (Week 1)
1. **Schema alignment and type definitions** - Prevents runtime errors
2. **Basic API service layer** - Core functionality
3. **CredentialManager API integration** - User-facing feature

### Phase 2: High Priority (Week 2)
1. **CredentialSelector component** - Workflow integration
2. **Secure credential value retrieval** - Security requirement
3. **Error handling and validation** - User experience

### Phase 3: Medium Priority (Week 3)
1. **Performance optimizations** - Caching, lazy loading
2. **Advanced UI features** - Search, filtering, update
3. **Comprehensive testing** - Quality assurance

### Phase 4: Low Priority (Week 4)
1. **Backend security enhancements** - Value exclusion from lists
2. **Credential types endpoint** - Enhanced UX
3. **Advanced workflow integration** - Type filtering, validation

This technical requirements document identifies all missing functionality and provides a clear roadmap for implementing a complete, secure, and performant credential management system.
