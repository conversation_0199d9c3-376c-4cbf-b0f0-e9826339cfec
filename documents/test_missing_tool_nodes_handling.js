/**
 * Test script to verify handling of missing tool nodes in schema
 */

// Simulate the agentic_schema.json structure with missing tool nodes
const testSchemaWithMissingNodes = {
  "workflow_data": {
    "nodes": [
      {
        "id": "start-node",
        "type": "WorkflowNode",
        "data": {
          "label": "Start",
          "type": "component",
          "originalType": "StartNode"
        }
      },
      {
        "id": "AgenticAI-1749976462114",
        "type": "WorkflowNode",
        "data": {
          "label": "AI Agent Executor",
          "type": "agent",
          "originalType": "AgenticAI",
          "config": {
            "tool_connections": {
              "tool_1": [
                {
                  "node_id": "MCP_Candidate_Interview_candidate_suitability-1749976479412",
                  "node_type": "MCP_Candidate_Interview_candidate_suitability",
                  "node_label": "Candidate Interview - candidate_suitability",
                  "component_definition": {
                    "display_name": "Candidate Interview - candidate_suitability"
                  }
                },
                {
                  "node_id": "MC<PERSON>_Tavily_Web_Search_and_Extraction_Server_tavily-crawl-1749976522030",
                  "node_type": "MCP_Tavily_Web_Search_and_Extraction_Server_tavily-crawl",
                  "node_label": "Tavily Web Search and Extraction Server - tavily-crawl",
                  "component_definition": {
                    "display_name": "Tavily Web Search and Extraction Server - tavily-crawl"
                  }
                },
                {
                  "node_id": "MCP_google-calendar-mcp_update_event-1749977346769",
                  "node_type": "MCP_google-calendar-mcp_update_event",
                  "node_label": "google-calendar-mcp - update_event",
                  "component_definition": {
                    "display_name": "google-calendar-mcp - update_event"
                  }
                },
                {
                  "node_id": "MCP_mcp-fetch_fetch-1749977364402",
                  "node_type": "MCP_mcp-fetch_fetch",
                  "node_label": "mcp-fetch - fetch",
                  "component_definition": {
                    "display_name": "mcp-fetch - fetch"
                  }
                }
              ]
            }
          }
        }
      }
    ],
    "edges": [
      {
        "id": "reactflow__edge-start-nodeflow-AgenticAI-1749976462114query",
        "source": "start-node",
        "sourceHandle": "flow",
        "target": "AgenticAI-1749976462114",
        "targetHandle": "query"
      }
    ]
  }
};

// Simulate the calculateToolConnectionState function with the updated logic
function testCalculateToolConnectionState() {
  console.log("Testing tool connection state with missing tool nodes...");
  
  const nodeId = "AgenticAI-1749976462114";
  const nodes = testSchemaWithMissingNodes.workflow_data.nodes;
  const edges = testSchemaWithMissingNodes.workflow_data.edges;
  
  const connectedTools = [];
  
  // Find the target node to check for config-based tool connections
  const targetNode = nodes.find(n => n.id === nodeId);
  
  // Method 1: Check for edge-based connections (current approach)
  const toolEdges = edges.filter(
    (edge) => edge.target === nodeId && edge.targetHandle && (
      /^tool_\d+$/.test(edge.targetHandle) || edge.targetHandle === "tools"
    )
  );
  
  console.log("Tool edges found:", toolEdges.length);
  
  toolEdges.forEach((edge) => {
    const sourceNode = nodes.find((n) => n.id === edge.source);
    if (sourceNode) {
      connectedTools.push({
        nodeId: edge.source,
        handleId: edge.targetHandle,
        componentType: sourceNode.data.originalType || "Unknown",
        label: sourceNode.data.label || `Node ${edge.source}`,
      });
    }
  });
  
  // Method 2: Check for config-based tool connections (for cases where tools are in config but nodes are missing)
  if (targetNode?.data.config?.tool_connections) {
    const toolConnections = targetNode.data.config.tool_connections;
    console.log("Found tool_connections in config:", Object.keys(toolConnections));
    
    // Handle both old format (tool_1, tool_2, etc.) and new format (tools)
    Object.entries(toolConnections).forEach(([handleId, connections]) => {
      console.log(`Processing handle ${handleId} with ${Array.isArray(connections) ? connections.length : 1} connections`);
      
      if (connections && Array.isArray(connections)) {
        connections.forEach((toolData) => {
          // Check if this tool is already in connectedTools (from edges)
          const existingTool = connectedTools.find(t => t.nodeId === toolData.node_id);
          if (!existingTool && toolData.node_id) {
            // Add tool from config even if node doesn't exist in nodes array
            connectedTools.push({
              nodeId: toolData.node_id,
              handleId: handleId,
              componentType: toolData.node_type || "MCP",
              label: toolData.node_label || toolData.component_definition?.display_name || "Unknown Tool",
            });
          }
        });
      } else if (connections && typeof connections === 'object') {
        // Handle legacy single-object format
        const connectionData = connections;
        const existingTool = connectedTools.find(t => t.nodeId === connectionData.node_id);
        if (!existingTool && connectionData.node_id) {
          connectedTools.push({
            nodeId: connectionData.node_id,
            handleId: handleId,
            componentType: connectionData.node_type || "MCP",
            label: connectionData.node_label || connectionData.component_definition?.display_name || "Unknown Tool",
          });
        }
      }
    });
  }
  
  const result = {
    connectedTools,
    toolCount: connectedTools.length,
    hasToolConnections: connectedTools.length > 0,
    hasConnectedTools: connectedTools.length > 0,
    connectedToolCount: connectedTools.length,
  };
  
  console.log("Tool connection state result:", result);
  
  // Verify results
  console.assert(result.toolCount === 4, `Expected 4 tools, got ${result.toolCount}`);
  console.assert(result.hasToolConnections === true, "Should have tool connections");
  console.assert(result.connectedTools.every(tool => tool.handleId === "tool_1"), "All tools should be on tool_1 handle");
  console.assert(result.connectedTools.every(tool => tool.componentType.includes("MCP")), "All tools should be MCP type");
  
  console.log("✅ Missing tool nodes handling test passed!");
  return result;
}

// Test the single handle approach with missing nodes
function testSingleHandleWithMissingNodes() {
  console.log("Testing single handle approach with missing tool nodes...");
  
  // Simulate converting the old tool_1 format to new tools format
  const nodeId = "AgenticAI-1749976462114";
  const nodes = testSchemaWithMissingNodes.workflow_data.nodes;
  const targetNode = nodes.find(n => n.id === nodeId);
  
  if (targetNode?.data.config?.tool_connections?.tool_1) {
    // Convert tool_1 connections to tools format
    const tool1Connections = targetNode.data.config.tool_connections.tool_1;
    
    // Simulate the new single handle approach
    const toolsConnections = tool1Connections.map(tool => ({
      ...tool,
      handleId: "tools" // Convert to single handle
    }));
    
    console.log("Converted to single handle format:", toolsConnections.length, "tools");
    
    // Verify conversion
    console.assert(toolsConnections.length === 4, "Should have 4 tools after conversion");
    console.assert(toolsConnections.every(tool => tool.node_id), "All tools should have node_id");
    console.assert(toolsConnections.every(tool => tool.node_label), "All tools should have labels");
    
    console.log("✅ Single handle conversion test passed!");
    return toolsConnections;
  }
  
  throw new Error("No tool_1 connections found for conversion test");
}

// Test unconnected node warnings prevention
function testUnconnectedNodeWarningsPrevention() {
  console.log("Testing unconnected node warnings prevention...");
  
  const nodes = testSchemaWithMissingNodes.workflow_data.nodes;
  const edges = testSchemaWithMissingNodes.workflow_data.edges;
  
  // Get all node IDs that exist in the workflow
  const existingNodeIds = new Set(nodes.map(n => n.id));
  
  // Get all node IDs referenced in tool connections
  const referencedToolNodeIds = new Set();
  
  nodes.forEach(node => {
    if (node.data.config?.tool_connections) {
      Object.values(node.data.config.tool_connections).forEach(connections => {
        if (Array.isArray(connections)) {
          connections.forEach(tool => {
            if (tool.node_id) {
              referencedToolNodeIds.add(tool.node_id);
            }
          });
        } else if (connections && typeof connections === 'object') {
          if (connections.node_id) {
            referencedToolNodeIds.add(connections.node_id);
          }
        }
      });
    }
  });
  
  // Find missing tool nodes (referenced but not existing)
  const missingToolNodes = Array.from(referencedToolNodeIds).filter(id => !existingNodeIds.has(id));
  
  console.log("Existing nodes:", existingNodeIds.size);
  console.log("Referenced tool nodes:", referencedToolNodeIds.size);
  console.log("Missing tool nodes:", missingToolNodes.length);
  console.log("Missing tool node IDs:", missingToolNodes);
  
  // Verify that we can identify missing nodes
  console.assert(missingToolNodes.length === 4, `Expected 4 missing nodes, got ${missingToolNodes.length}`);
  console.assert(missingToolNodes.includes("MCP_Candidate_Interview_candidate_suitability-1749976479412"), "Should include candidate interview tool");
  console.assert(missingToolNodes.includes("MCP_Tavily_Web_Search_and_Extraction_Server_tavily-crawl-1749976522030"), "Should include tavily tool");
  
  console.log("✅ Unconnected node warnings prevention test passed!");
  return { existingNodeIds, referencedToolNodeIds, missingToolNodes };
}

// Run all tests
function runAllTests() {
  console.log("🚀 Running missing tool nodes handling tests...\n");
  
  try {
    testCalculateToolConnectionState();
    console.log("");
    
    testSingleHandleWithMissingNodes();
    console.log("");
    
    testUnconnectedNodeWarningsPrevention();
    console.log("");
    
    console.log("🎉 All tests passed! Missing tool nodes handling is working correctly.");
    
    // Summary
    console.log("\n📋 Missing Tool Nodes Handling Summary:");
    console.log("✅ Tool connections can be read from config even when nodes are missing");
    console.log("✅ Single handle approach works with config-based tool connections");
    console.log("✅ Missing tool nodes can be identified to prevent warnings");
    console.log("✅ Tool connection state calculation handles both edge and config sources");
    console.log("✅ Backward compatibility maintained for existing schemas");
    
  } catch (error) {
    console.error("❌ Test failed:", error);
  }
}

// Run the tests
runAllTests();