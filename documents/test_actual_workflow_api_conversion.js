/**
 * Test the actual workflow API conversion logic to ensure it matches our simulation
 */

// Import the actual workflow API function (simulated)
function simulateActualWorkflowAPILogic(nodes, edges, fieldValues) {
  console.log("🔄 Testing Actual Workflow API Conversion Logic");
  console.log("=" .repeat(50));
  
  // Find the StartNode
  const startNode = nodes.find((node) => node.data.originalType === "StartNode");
  if (!startNode) {
    console.log("❌ No StartNode found");
    return null;
  }
  
  // Initialize collected parameters
  const collectedParameters = { ...(startNode.data.config?.collected_parameters || {}) };
  
  // Filter nodes (simplified for test)
  const filteredNodes = nodes;
  
  // Collect tool connection information for AgenticAI nodes after filtering
  const agenticAINodes = filteredNodes.filter(node => node.data.originalType === "AgenticAI");
  
  agenticAINodes.forEach(agenticNode => {
    if (agenticNode.data.config?.tool_connections) {
      console.log(`\n📋 Processing AgenticAI node: ${agenticNode.id}`);
      
      // Process tool connections - handle both array and legacy single connection formats
      const toolConnections = agenticNode.data.config.tool_connections;
      const processedToolConnections = {};
      
      console.log("Original format check:");
      Object.entries(toolConnections).forEach(([handle, connections]) => {
        console.log(`  ${handle}: ${Array.isArray(connections) ? 'Array (new)' : 'Object (legacy)'}`);
      });
      
      Object.entries(toolConnections).forEach(([handle, connections]) => {
        if (Array.isArray(connections)) {
          // New array format - multiple tools per handle
          processedToolConnections[handle] = connections;
          console.log(`✅ Handle ${handle}: ${connections.length} tool connections (already array format)`);
        } else {
          // Legacy single connection format - convert to array
          processedToolConnections[handle] = [connections];
          console.log(`🔄 Handle ${handle}: 1 tool connection (converted from legacy format)`);
          console.log(`   Converted: ${connections.node_label} (${connections.node_type})`);
        }
      });
      
      // Store processed tool connection data in the StartNode for execution context
      const toolConnectionsKey = `${agenticNode.id}_tool_connections`;
      collectedParameters[toolConnectionsKey] = {
        node_id: agenticNode.id,
        node_name: agenticNode.data.label || "AgenticAI Node",
        input_name: "tool_connections",
        value: processedToolConnections,
        connected_to_start: true,
        connected_as_tool: false,
        is_tool_connection_data: true,
      };
      
      console.log(`📊 Final processed connections for ${agenticNode.id}:`, JSON.stringify(processedToolConnections, null, 2));
    }
  });
  
  return {
    startNode: {
      ...startNode,
      data: {
        ...startNode.data,
        config: {
          ...startNode.data.config,
          collected_parameters: collectedParameters,
        }
      }
    },
    collectedParameters
  };
}

// Test data scenarios
const testScenarios = [
  {
    name: "Legacy Single Connection Format",
    nodes: [
      {
        id: "start-node",
        data: { 
          originalType: "StartNode",
          config: { collected_parameters: {} }
        }
      },
      {
        id: "agentic-ai-1",
        data: {
          originalType: "AgenticAI",
          label: "Legacy AI Agent",
          config: { 
            tool_connections: {
              "tool_1": {
                node_id: "legacy-tool",
                node_type: "LegacyTool",
                node_label: "Legacy Tool",
                component_definition: { name: "LegacyTool" }
              }
            }
          }
        }
      }
    ]
  },
  {
    name: "New Array Format",
    nodes: [
      {
        id: "start-node",
        data: { 
          originalType: "StartNode",
          config: { collected_parameters: {} }
        }
      },
      {
        id: "agentic-ai-1",
        data: {
          originalType: "AgenticAI",
          label: "New AI Agent",
          config: { 
            tool_connections: {
              "tool_1": [
                {
                  node_id: "new-tool-1",
                  node_type: "NewTool",
                  node_label: "New Tool 1",
                  component_definition: { name: "NewTool" }
                },
                {
                  node_id: "new-tool-2",
                  node_type: "NewTool",
                  node_label: "New Tool 2", 
                  component_definition: { name: "NewTool" }
                }
              ]
            }
          }
        }
      }
    ]
  },
  {
    name: "Mixed Format",
    nodes: [
      {
        id: "start-node",
        data: { 
          originalType: "StartNode",
          config: { collected_parameters: {} }
        }
      },
      {
        id: "agentic-ai-1",
        data: {
          originalType: "AgenticAI",
          label: "Mixed AI Agent",
          config: { 
            tool_connections: {
              "tool_1": {
                node_id: "legacy-tool",
                node_type: "LegacyTool",
                node_label: "Legacy Tool",
                component_definition: { name: "LegacyTool" }
              },
              "tool_2": [
                {
                  node_id: "new-tool-1",
                  node_type: "NewTool",
                  node_label: "New Tool 1",
                  component_definition: { name: "NewTool" }
                }
              ]
            }
          }
        }
      }
    ]
  }
];

// Run tests for each scenario
function runConversionTests() {
  console.log("🧪 Testing Actual Workflow API Conversion Logic");
  console.log("=" .repeat(60));
  
  const results = [];
  
  testScenarios.forEach((scenario, index) => {
    console.log(`\n${index + 1}. Testing: ${scenario.name}`);
    console.log("-" .repeat(40));
    
    const result = simulateActualWorkflowAPILogic(scenario.nodes, [], {});
    const toolConnectionData = result?.collectedParameters["agentic-ai-1_tool_connections"];
    
    if (toolConnectionData) {
      const processedConnections = toolConnectionData.value;
      
      // Verify all connections are arrays
      const allAreArrays = Object.values(processedConnections).every(conn => Array.isArray(conn));
      
      console.log(`✅ Result: All connections converted to arrays: ${allAreArrays ? 'YES' : 'NO'}`);
      
      // Log details
      Object.entries(processedConnections).forEach(([handle, connections]) => {
        console.log(`   ${handle}: ${Array.isArray(connections) ? 'Array' : 'Not Array'} (length: ${connections?.length || 0})`);
      });
      
      results.push({
        scenario: scenario.name,
        success: allAreArrays,
        processedConnections
      });
    } else {
      console.log("❌ No tool connection data found");
      results.push({
        scenario: scenario.name,
        success: false,
        processedConnections: null
      });
    }
  });
  
  return results;
}

// Execute tests
console.log("🚀 Starting Actual Workflow API Conversion Tests\n");

const testResults = runConversionTests();

console.log("\n📈 Final Test Results:");
console.log("=" .repeat(40));

let allPassed = true;
testResults.forEach((result, index) => {
  const status = result.success ? '✅ PASS' : '❌ FAIL';
  console.log(`${index + 1}. ${result.scenario}: ${status}`);
  if (!result.success) allPassed = false;
});

console.log(`\nOverall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);

if (allPassed) {
  console.log("\n🎉 Workflow API conversion logic is working correctly!");
  console.log("   - Legacy format properly converted to arrays");
  console.log("   - New format preserved as arrays");
  console.log("   - Mixed formats handled correctly");
  console.log("   - All tool connections are consistently stored as arrays");
} else {
  console.log("\n⚠️  Some conversion issues detected in the workflow API logic.");
}

// Test edge case: empty tool connections
console.log("\n🔍 Testing Edge Cases:");
console.log("-" .repeat(30));

const edgeCaseNode = {
  id: "agentic-ai-empty",
  data: {
    originalType: "AgenticAI",
    label: "Empty AI Agent",
    config: { 
      tool_connections: {} // Empty object
    }
  }
};

const emptyResult = simulateActualWorkflowAPILogic([
  { id: "start-node", data: { originalType: "StartNode", config: { collected_parameters: {} } } },
  edgeCaseNode
], [], {});

const emptyToolData = emptyResult?.collectedParameters["agentic-ai-empty_tool_connections"];
console.log(`Empty tool_connections handled: ${emptyToolData ? '✅ YES' : '❌ NO'}`);
if (emptyToolData) {
  console.log(`Empty object processed correctly: ${Object.keys(emptyToolData.value).length === 0 ? '✅ YES' : '❌ NO'}`);
}