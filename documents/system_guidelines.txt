 Workflow Component Architecture
- Parameters with default values in component definitions should NOT be included in StartNode's collected_parameters to avoid unnecessary user prompts.
- For workflow components, if the user doesn't connect a handle to an input, it should be asked in the StartNode config for user input collection.
- User prefers conditional nodes to be treated as separate workflow components that generate independent transitions with dedicated IDs, executing as standalone components rather than being embedded within other transitions.
- User prefers conditional nodes to route to the exact transition specified in the routing_decision's target_transition field based on which condition matched.
- User wants conditional nodes to support multiple simultaneous transitions when multiple conditions match, requiring orchestration-engine to process multiple transition IDs in parallel.
- Conditional nodes should use routing decisions only for workflow routing, not pass routing metadata as data to downstream components.
- User prefers to remove redundant components (like switch case) when functionality is consolidated elsewhere, avoiding changes to orchestration engine.
- User prefers to remove legacy embedded routing code in favor of component-based implementation when proven to work.

# Handle Configuration & UI
- User expects conditional nodes to have dynamic input/output handles that appear/disappear based on configuration.
- User wants conditional node with num_additional_conditions=2 to have 3 input handles and 4 output handles, treating primary input as condition 1.
- User prefers dual-purpose handle approach where input data handles serve both as connection points and variable name fields, eliminating separate configuration fields.
- User expects AI components to follow the dual-purpose handle pattern from create_dual_purpose_input() utility for consistency with other workflow components.
- User prefers component inspector panels to follow standard configuration patterns with essential fields only, avoiding visualization sections in favor of clean, consistent UI.
- User wants to be able to manually provide configuration JSON directly through the inspector panel for dynamic handle components.

# Agent & AI Component Integration
- Agent configurations should always include id and name fields, and default values for all configurable parameters must be preserved even when users don't explicitly modify them.
- User prefers AgenticAI agents to control ALL component inputs (both handle inputs and direct parameters) rather than maintaining separate handle connections when components are used as tools.
- User prefers AgenticAI component to support direct multiple node connections to workflow_components handle, bypassing one-to-one constraint.
- User has separate agent-service that handles AgenticAI execution via Kafka from orchestration engine.
- User wants to include MCP marketplace components as tools in AgenticAI workflow component integration.
- User wants to connect other workflow components (like alter metadata component) as tools within agents.
- Tool-connected components in AgenticAI should be visually differentiated similar to 'requires approval' nodes and treated as part of workflow execution flow even when only indirectly connected to start node through tool handles.
- User prefers visual changes to nodes for tool connections rather than changes to edge styling for differentiation.
- User prefers Option A for workflow component tool integration: sending component schemas in agent_config.tools for agent platform to create tool functions.

# Implementation Practices
- User requires TDD methodology with strict Red-Green-Refactor cycles, 95%+ test coverage, SOLID design principles, DRY coding practices, comprehensive error handling with type hints, performance benchmarks, modular code with single-responsibility functions, comprehensive error handling with logging, and asking clarifying questions as a senior developer when requirements are unclear or architectural decisions need validation.
- User prefers comprehensive analysis and planning before implementation, requiring detailed technical specifications and documentation before writing code.
- User prefers generalized solutions that work across multiple components rather than single-component specific implementations.
- User prefers to disable SSL verification for API requests rather than implementing full SSL configuration when encountering certificate issues.
- User prefers comprehensive code cleanup that removes redundant legacy code while preserving safety measures and critical component fallbacks.
- User prefers comprehensive logging to understand workflow execution flow and identify errors during debugging.
- User prefers file refactoring to target 500-600 lines maximum with incremental changes and strict preservation of API interfaces/function signatures/backward compatibility.
- User prefers to complete all phases sequentially according to the task list and requires tasks to be marked as complete before proceeding to the next task.
- User wants tasks to be marked as complete once they are finished during implementation.

# Component Functionality
- User wants Message to Data component to convert stringified JSON (including malformed JSON with escape characters) into valid JSON objects, not just field extraction.
- User wants component discovery to always fetch with force_refresh=true to ensure fresh data retrieval every time.
- User prefers dynamic input type handling systems that automatically process JSON Schema types without manual case-by-case additions, with fallback rendering and automatic validation.
- User wants the dynamic input type handling system applied universally to all workflow components, not limited to MCP marketplace components.