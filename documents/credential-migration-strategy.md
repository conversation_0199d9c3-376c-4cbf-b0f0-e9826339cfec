# Credential Management Migration Strategy

## Migration Overview

This document outlines the step-by-step strategy for migrating from the current localStorage-based mock credential system to a fully integrated API-based credential management system.

## Pre-Migration Assessment

### Current System Dependencies
1. **CredentialManager Component** - Uses mock functions from `api.ts`
2. **Inspector Panel** - Loads credentials for component configuration
3. **InputRenderer** - Credential input type with mock integration
4. **Workflow Execution** - Uses `mockGetCredentialValue()` for credential retrieval
5. **Test Scripts** - `test-credentials.js` uses mock functions

### Data Migration Considerations
- **Existing localStorage Data**: Users may have credentials stored in localStorage
- **Data Format Differences**: Mock vs API schema differences
- **User Impact**: Seamless transition without data loss

## Migration Phases

### Phase 1: Foundation Setup (Days 1-2)

#### 1.1 Type System Alignment
**Priority**: Critical
**Estimated Time**: 4 hours

```typescript
// Create: src/types/credentials.ts
export interface CredentialCreateRequest {
  key_name: string;        // Aligned with backend
  value: string;
  description?: string;    // New field
}

export interface CredentialUpdateRequest {
  key_name?: string;
  value?: string;
  description?: string;
}

export interface Credential {
  id: string;
  name: string;           // Mapped from key_name
  description?: string;
  createdAt: string;      // Mapped from created_at
  updatedAt: string;      // Mapped from updated_at
  lastUsedAt: string;     // Mapped from last_used_at
}

// Transformation utilities
export const transformToBackend = (frontend: CredentialCreate): CredentialCreateRequest
export const transformFromBackend = (backend: BackendCredential): Credential
```

#### 1.2 API Configuration Setup
**Priority**: Critical
**Estimated Time**: 2 hours

```typescript
// Update: src/lib/apiConfig.ts
export const API_ENDPOINTS = {
  // ... existing endpoints
  CREDENTIALS: {
    LIST: `${API_BASE_URL}/credentials`,
    CREATE: `${API_BASE_URL}/credentials`,
    GET: (id: string) => `${API_BASE_URL}/credentials/${id}`,
    UPDATE: (id: string) => `${API_BASE_URL}/credentials/${id}`,
    DELETE: (id: string) => `${API_BASE_URL}/credentials/${id}`,
  }
};
```

#### 1.3 Service Layer Foundation
**Priority**: Critical
**Estimated Time**: 6 hours

```typescript
// Create: src/services/credentialService.ts
export class CredentialService {
  private api: AxiosInstance;
  
  constructor() {
    this.api = createAxiosInstance({
      enableTokenRefresh: true,
      enableClientSideToken: true,
    });
  }

  async fetchCredentials(): Promise<CredentialListResponse> {
    const response = await this.api.get(API_ENDPOINTS.CREDENTIALS.LIST);
    return {
      credentials: response.data.credentials.map(transformFromBackend)
    };
  }

  // ... other methods
}
```

### Phase 2: Core API Integration (Days 3-4)

#### 2.1 Replace Mock Functions
**Priority**: High
**Estimated Time**: 8 hours

**Strategy**: Gradual replacement with feature flags

```typescript
// Update: src/lib/api.ts
const USE_REAL_API = process.env.NEXT_PUBLIC_USE_CREDENTIAL_API === 'true';

export async function fetchCredentials(): Promise<CredentialListResponse> {
  if (USE_REAL_API) {
    return credentialService.fetchCredentials();
  } else {
    return mockFetchCredentials();
  }
}

export async function createCredential(credential: CredentialCreate): Promise<Credential> {
  if (USE_REAL_API) {
    return credentialService.createCredential(transformToBackend(credential));
  } else {
    return mockCreateCredential(credential);
  }
}

export async function deleteCredential(credentialId: string): Promise<CredentialDeleteResponse> {
  if (USE_REAL_API) {
    await credentialService.deleteCredential(credentialId);
    return { success: true };
  } else {
    return mockDeleteCredential(credentialId);
  }
}
```

#### 2.2 Error Handling Implementation
**Priority**: High
**Estimated Time**: 4 hours

```typescript
// Create: src/utils/credentialErrorHandler.ts
export enum CredentialErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  SERVER_ERROR = 'SERVER_ERROR'
}

export const handleCredentialError = (error: any): CredentialError => {
  if (error.response?.status === 401 || error.response?.status === 403) {
    return {
      type: CredentialErrorType.AUTHENTICATION_ERROR,
      message: 'Authentication required. Please log in again.',
    };
  }
  
  if (error.response?.status === 404) {
    return {
      type: CredentialErrorType.NOT_FOUND_ERROR,
      message: 'Credential not found.',
    };
  }
  
  if (error.response?.status >= 400 && error.response?.status < 500) {
    return {
      type: CredentialErrorType.VALIDATION_ERROR,
      message: error.response.data?.message || 'Invalid credential data.',
      field: error.response.data?.field,
    };
  }
  
  return {
    type: CredentialErrorType.NETWORK_ERROR,
    message: 'Network error. Please try again.',
  };
};
```

#### 2.3 Data Migration Utility
**Priority**: Medium
**Estimated Time**: 4 hours

```typescript
// Create: src/utils/credentialMigration.ts
export class CredentialMigrationService {
  async migrateLocalStorageCredentials(): Promise<void> {
    const localCredentials = this.getLocalStorageCredentials();
    
    if (localCredentials.length === 0) {
      return;
    }

    console.log(`Migrating ${localCredentials.length} credentials from localStorage`);
    
    for (const credential of localCredentials) {
      try {
        const value = this.getLocalStorageCredentialValue(credential.id);
        if (value) {
          await credentialService.createCredential({
            key_name: credential.name,
            value: value,
            description: `Migrated from localStorage - Type: ${credential.type}`,
          });
        }
      } catch (error) {
        console.error(`Failed to migrate credential ${credential.id}:`, error);
      }
    }
    
    // Clear localStorage after successful migration
    this.clearLocalStorageCredentials();
  }

  private getLocalStorageCredentials(): Credential[] {
    // Implementation to read from localStorage
  }

  private clearLocalStorageCredentials(): void {
    // Implementation to clear localStorage
  }
}
```

### Phase 3: UI Component Updates (Days 5-6)

#### 3.1 CredentialManager Enhancement
**Priority**: High
**Estimated Time**: 6 hours

```typescript
// Update: src/components/credentials/CredentialManager.tsx
interface CredentialFormData {
  name: string;           // Will be mapped to key_name
  description: string;    // New field
  value: string;
}

const CredentialManager: React.FC = () => {
  const [formData, setFormData] = useState<CredentialFormData>({
    name: "",
    description: "",
    value: "",
  });

  // Add description field to form
  // Add update functionality
  // Improve error handling
  // Add loading states for individual operations
};
```

#### 3.2 Credential Selector Component
**Priority**: High
**Estimated Time**: 8 hours

```typescript
// Create: src/components/credentials/CredentialSelector.tsx
interface CredentialSelectorProps {
  value?: string;
  onChange: (credentialId: string) => void;
  credentialType?: string;
  placeholder?: string;
  disabled?: boolean;
  required?: boolean;
}

export const CredentialSelector: React.FC<CredentialSelectorProps> = ({
  value,
  onChange,
  credentialType,
  placeholder = "Select a credential...",
  disabled = false,
  required = false,
}) => {
  const [credentials, setCredentials] = useState<Credential[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Implementation with real API integration
  // Search and filter functionality
  // Loading states and error handling
  // Create new credential option
};
```

#### 3.3 Inspector Panel Integration
**Priority**: Medium
**Estimated Time**: 4 hours

```typescript
// Update: src/components/inspector/InputRenderer.tsx
case "credential":
  return (
    <div className="mt-1 space-y-2">
      <div className="flex items-center space-x-2">
        <Switch
          id={`${inputId}-use-credential`}
          checked={value?.use_credential_id || false}
          onCheckedChange={(checked) => {
            onChange(inputDef.name, {
              ...(value || {}),
              use_credential_id: checked,
            });
          }}
          disabled={isDisabled}
        />
        <Label htmlFor={`${inputId}-use-credential`}>
          Use stored credential
        </Label>
      </div>

      {value?.use_credential_id ? (
        <CredentialSelector
          value={value?.credential_id}
          onChange={(credentialId) => {
            onChange(inputDef.name, {
              ...(value || {}),
              credential_id: credentialId,
            });
          }}
          credentialType={inputDef.credential_type}
          disabled={isDisabled}
        />
      ) : (
        <Input
          type="password"
          value={value?.value || ""}
          onChange={(e) => {
            onChange(inputDef.name, {
              ...(value || {}),
              value: e.target.value,
            });
          }}
          placeholder={`Enter ${inputDef.display_name}...`}
          disabled={isDisabled}
        />
      )}
    </div>
  );
```

### Phase 4: Testing and Validation (Days 7-8)

#### 4.1 Unit Test Implementation
**Priority**: High
**Estimated Time**: 8 hours

```typescript
// Create: src/services/__tests__/credentialService.test.ts
describe('CredentialService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('fetchCredentials', () => {
    it('should fetch and transform credentials correctly', async () => {
      // Mock API response
      // Test transformation
      // Verify result
    });

    it('should handle authentication errors', async () => {
      // Mock 401 response
      // Verify error handling
    });
  });

  // ... more test cases
});

// Create: src/components/credentials/__tests__/CredentialManager.test.tsx
// Create: src/components/credentials/__tests__/CredentialSelector.test.tsx
```

#### 4.2 Integration Testing
**Priority**: Medium
**Estimated Time**: 4 hours

```typescript
// Create: src/__tests__/integration/credentialFlow.test.tsx
describe('Credential Management Flow', () => {
  it('should complete full credential lifecycle', async () => {
    // Create credential
    // List credentials
    // Update credential
    // Delete credential
    // Verify each step
  });

  it('should handle workflow integration', async () => {
    // Create credential
    // Use in workflow component
    // Verify credential selection
    // Test execution context
  });
});
```

### Phase 5: Performance Optimization (Days 9-10)

#### 5.1 Caching Implementation
**Priority**: Medium
**Estimated Time**: 4 hours

```typescript
// Create: src/utils/credentialCache.ts
class CredentialCache {
  private cache = new Map<string, CachedCredential>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutes

  get(key: string): Credential[] | null {
    const cached = this.cache.get(key);
    if (!cached || Date.now() > cached.expiry) {
      this.cache.delete(key);
      return null;
    }
    return cached.data;
  }

  set(key: string, data: Credential[]): void {
    this.cache.set(key, {
      data,
      expiry: Date.now() + this.TTL,
    });
  }

  invalidate(pattern?: string): void {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }
}
```

#### 5.2 Performance Monitoring
**Priority**: Low
**Estimated Time**: 2 hours

```typescript
// Create: src/utils/performanceMonitor.ts
export const measureCredentialOperation = async <T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> => {
  const start = performance.now();
  try {
    const result = await fn();
    const duration = performance.now() - start;
    console.log(`Credential ${operation} completed in ${duration.toFixed(2)}ms`);
    return result;
  } catch (error) {
    const duration = performance.now() - start;
    console.error(`Credential ${operation} failed after ${duration.toFixed(2)}ms:`, error);
    throw error;
  }
};
```

## Rollback Strategy

### Feature Flag Control
```typescript
// Environment variable control
const CREDENTIAL_API_ENABLED = process.env.NEXT_PUBLIC_CREDENTIAL_API_ENABLED === 'true';

// Gradual rollout capability
const CREDENTIAL_API_ROLLOUT_PERCENTAGE = parseInt(
  process.env.NEXT_PUBLIC_CREDENTIAL_API_ROLLOUT || '0'
);

// User-based rollout
const shouldUseCredentialAPI = (userId: string): boolean => {
  if (!CREDENTIAL_API_ENABLED) return false;
  
  const hash = simpleHash(userId);
  return (hash % 100) < CREDENTIAL_API_ROLLOUT_PERCENTAGE;
};
```

### Emergency Rollback
1. **Immediate**: Set `NEXT_PUBLIC_CREDENTIAL_API_ENABLED=false`
2. **Gradual**: Reduce `NEXT_PUBLIC_CREDENTIAL_API_ROLLOUT` percentage
3. **User-specific**: Maintain user-level override capability

## Success Metrics

### Technical Metrics
- ✅ All API endpoints responding < 100ms
- ✅ Zero data loss during migration
- ✅ 95%+ test coverage achieved
- ✅ No authentication-related errors

### User Experience Metrics
- ✅ Credential operations complete < 500ms
- ✅ Zero user-reported data loss
- ✅ Improved error messages and feedback
- ✅ Seamless transition experience

### Business Metrics
- ✅ Increased credential usage in workflows
- ✅ Reduced support tickets related to credentials
- ✅ Enhanced security posture
- ✅ Foundation for advanced credential features

This migration strategy ensures a smooth, low-risk transition from the mock credential system to a fully integrated API-based solution while maintaining data integrity and user experience throughout the process.
