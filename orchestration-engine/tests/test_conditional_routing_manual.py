#!/usr/bin/env python3
"""
Manual test runner for conditional routing handler multiple transitions support.
This script tests the orchestration engine changes without requiring pytest.
"""

import asyncio
import sys
import os
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from core_.conditional_routing_handler import ConditionalRoutingHandler


class MockLogger:
    """Mock logger for testing."""
    def __init__(self):
        self.info_calls = []
        self.warning_calls = []
        self.error_calls = []
        self.debug_calls = []
    
    def info(self, msg):
        self.info_calls.append(msg)
        print(f"INFO: {msg}")
    
    def warning(self, msg):
        self.warning_calls.append(msg)
        print(f"WARNING: {msg}")
    
    def error(self, msg, exc_info=None):
        self.error_calls.append(msg)
        print(f"ERROR: {msg}")
    
    def debug(self, msg):
        self.debug_calls.append(msg)
        print(f"DEBUG: {msg}")


async def test_single_transition_legacy_format():
    """Test handling of legacy single transition format."""
    print("🧪 Testing legacy single transition format...")
    
    logger = MockLogger()
    handler = ConditionalRoutingHandler(logger=logger)
    
    execution_result = {
        "status": "success",
        "routing_decision": {
            "target_transition": "transition_1",
            "matched_condition": 1,
            "condition_result": True,
            "execution_time_ms": 15.5
        }
    }
    
    transition = {"id": "test_transition"}
    
    result = await handler.handle_conditional_result(execution_result, transition)
    
    print(f"   Result: {result}")
    print(f"   Info calls: {len(logger.info_calls)}")
    
    assert result == ["transition_1"]
    assert len(logger.info_calls) >= 1
    
    print("   ✅ PASSED: Legacy format handled correctly")


async def test_multiple_transitions_new_format():
    """Test handling of new multiple transitions format."""
    print("\n🧪 Testing new multiple transitions format...")
    
    logger = MockLogger()
    handler = ConditionalRoutingHandler(logger=logger)
    
    execution_result = {
        "status": "success",
        "routing_decision": {
            "target_transitions": ["transition_1", "transition_2", "transition_3"],
            "matched_conditions": [1, 2, 3],
            "condition_result": True,
            "execution_time_ms": 25.8,
            "evaluation_strategy": "all_matches"
        }
    }
    
    transition = {"id": "test_transition"}
    
    result = await handler.handle_conditional_result(execution_result, transition)
    
    print(f"   Result: {result}")
    print(f"   Info calls: {len(logger.info_calls)}")
    
    assert result == ["transition_1", "transition_2", "transition_3"]
    assert len(logger.info_calls) >= 1
    
    print("   ✅ PASSED: Multiple transitions format handled correctly")


async def test_single_transition_new_format():
    """Test handling of single transition in new array format."""
    print("\n🧪 Testing single transition in new array format...")
    
    logger = MockLogger()
    handler = ConditionalRoutingHandler(logger=logger)
    
    execution_result = {
        "status": "success",
        "routing_decision": {
            "target_transitions": ["transition_1"],
            "matched_conditions": [1],
            "condition_result": True,
            "execution_time_ms": 12.3,
            "evaluation_strategy": "all_matches"
        }
    }
    
    transition = {"id": "test_transition"}
    
    result = await handler.handle_conditional_result(execution_result, transition)
    
    print(f"   Result: {result}")
    
    assert result == ["transition_1"]
    
    print("   ✅ PASSED: Single transition in array format handled correctly")


async def test_no_matches_new_format():
    """Test handling of no matches with default transition in new format."""
    print("\n🧪 Testing no matches with default transition...")
    
    logger = MockLogger()
    handler = ConditionalRoutingHandler(logger=logger)
    
    execution_result = {
        "status": "success",
        "routing_decision": {
            "target_transitions": ["default_transition"],
            "matched_conditions": [],
            "condition_result": False,
            "execution_time_ms": 8.1,
            "evaluation_strategy": "all_matches"
        }
    }
    
    transition = {"id": "test_transition"}
    
    result = await handler.handle_conditional_result(execution_result, transition)
    
    print(f"   Result: {result}")
    
    assert result == ["default_transition"]
    
    print("   ✅ PASSED: No matches defaults handled correctly")


async def test_missing_routing_decision():
    """Test handling of execution result without routing decision."""
    print("\n🧪 Testing missing routing decision...")
    
    logger = MockLogger()
    handler = ConditionalRoutingHandler(logger=logger)
    
    execution_result = {
        "status": "success",
        "some_other_data": "value"
    }
    
    transition = {"id": "test_transition"}
    
    result = await handler.handle_conditional_result(execution_result, transition)
    
    print(f"   Result: {result}")
    print(f"   Warning calls: {len(logger.warning_calls)}")
    
    assert result == []
    assert len(logger.warning_calls) >= 1
    
    print("   ✅ PASSED: Missing routing decision handled correctly")


async def test_empty_target_transitions():
    """Test handling of empty target transitions array."""
    print("\n🧪 Testing empty target transitions...")
    
    logger = MockLogger()
    handler = ConditionalRoutingHandler(logger=logger)
    
    execution_result = {
        "status": "success",
        "routing_decision": {
            "target_transitions": [],
            "matched_conditions": [],
            "condition_result": False,
            "execution_time_ms": 5.0
        }
    }
    
    transition = {"id": "test_transition"}
    
    result = await handler.handle_conditional_result(execution_result, transition)
    
    print(f"   Result: {result}")
    print(f"   Warning calls: {len(logger.warning_calls)}")
    
    assert result == []
    assert len(logger.warning_calls) >= 1
    
    print("   ✅ PASSED: Empty transitions handled correctly")


def test_extract_target_transitions_new_format():
    """Test _extract_target_transitions with new array format."""
    print("\n🧪 Testing extract target transitions (new format)...")
    
    logger = MockLogger()
    handler = ConditionalRoutingHandler(logger=logger)
    
    routing_decision = {
        "target_transitions": ["transition_1", "transition_2"],
        "matched_conditions": [1, 2]
    }
    
    result = handler._extract_target_transitions(routing_decision, "test_id")
    
    print(f"   Result: {result}")
    
    assert result == ["transition_1", "transition_2"]
    
    print("   ✅ PASSED: New format extraction works correctly")


def test_extract_target_transitions_legacy_format():
    """Test _extract_target_transitions with legacy single format."""
    print("\n🧪 Testing extract target transitions (legacy format)...")
    
    logger = MockLogger()
    handler = ConditionalRoutingHandler(logger=logger)
    
    routing_decision = {
        "target_transition": "transition_1",
        "matched_condition": 1
    }
    
    result = handler._extract_target_transitions(routing_decision, "test_id")
    
    print(f"   Result: {result}")
    
    assert result == ["transition_1"]
    
    print("   ✅ PASSED: Legacy format extraction works correctly")


def test_extract_target_transitions_with_none_values():
    """Test _extract_target_transitions filtering None values."""
    print("\n🧪 Testing extract target transitions with None values...")
    
    logger = MockLogger()
    handler = ConditionalRoutingHandler(logger=logger)
    
    routing_decision = {
        "target_transitions": ["transition_1", None, "transition_2", None],
        "matched_conditions": [1, 3]
    }
    
    result = handler._extract_target_transitions(routing_decision, "test_id")
    
    print(f"   Result: {result}")
    
    # None values should be filtered out
    assert result == ["transition_1", "transition_2"]
    
    print("   ✅ PASSED: None values filtered correctly")


async def main():
    """Run all tests."""
    print("🚀 Starting Conditional Routing Handler Multiple Transitions Tests\n")
    
    try:
        await test_single_transition_legacy_format()
        await test_multiple_transitions_new_format()
        await test_single_transition_new_format()
        await test_no_matches_new_format()
        await test_missing_routing_decision()
        await test_empty_target_transitions()
        
        test_extract_target_transitions_new_format()
        test_extract_target_transitions_legacy_format()
        test_extract_target_transitions_with_none_values()
        
        print("\n🎉 ALL TESTS PASSED! Orchestration engine multiple transitions support is working correctly.")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
