import asyncio
import logging
import json
import uuid
from typing import Dict, Any
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer  # type: ignore
import sys
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


# Kafka configuration
KAFKA_BOOTSTRAP_SERVERS = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9092")
KAFKA_AGENT_CREATION_TOPIC = os.getenv(
    "KAFKA_AGENT_CREATION_TOPIC", "agent_creation_requests"
)
KAFKA_AGENT_SESSION_DELETION_TOPIC = os.getenv(
    "KAFKA_AGENT_SESSION_DELETION_TOPIC", "agent_session_deletion_requests"
)
KAFKA_AGENT_RESPONSE_TOPIC = os.getenv(
    "KAFKA_AGENT_RESPONSE_TOPIC", "agent_chat_responses"
)


class SessionDeletionTestClient:
    def __init__(self):
        self.producer = None
        self.consumer = None
        self._initialized = False

    async def initialize(self):
        if self._initialized:
            return

        self.producer = AIOKafkaProducer(
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            max_request_size=524288000,
        )
        self.consumer = AIOKafkaConsumer(
            KAFKA_AGENT_RESPONSE_TOPIC,
            bootstrap_servers=KAFKA_BOOTSTRAP_SERVERS,
            group_id=f"test-deletion-group-{uuid.uuid4()}",
            auto_offset_reset="latest",
            enable_auto_commit=True,
        )

        await self.producer.start()
        await self.consumer.start()
        self._initialized = True
        logger.info("Session Deletion test client initialized")

    async def cleanup(self):
        if self.producer:
            await self.producer.stop()
        if self.consumer:
            await self.consumer.stop()

    async def send_message(
        self, topic: str, message: Dict[str, Any], headers: list = None
    ) -> None:
        try:
            value = json.dumps(message).encode("utf-8")
            await self.producer.send_and_wait(topic, value=value, headers=headers)
            logger.info(f"Message sent to topic {topic}")
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise

    async def listen_for_responses(self, correlation_id: str = None, timeout: int = 30):
        """Listen for responses with correlation ID matching"""
        try:
            start_time = asyncio.get_event_loop().time()
            responses = []
            logger.info(f"Listening for correlation_id: {correlation_id}")

            while True:
                try:
                    msg = await asyncio.wait_for(
                        self.consumer.getone(), timeout=timeout
                    )

                    current_time = asyncio.get_event_loop().time()
                    if current_time - start_time > timeout:
                        logger.warning("Response listening timeout reached")
                        break

                    # Process message headers
                    msg_correlation_id = None
                    if msg.headers:
                        headers_dict = {
                            k: v.decode("utf-8") if isinstance(v, bytes) else v
                            for k, v in msg.headers
                        }
                        msg_correlation_id = headers_dict.get("correlationId")

                    # Decode and process message value
                    try:
                        response = json.loads(msg.value.decode("utf-8"))

                        # Check correlation ID match
                        if correlation_id and msg_correlation_id == correlation_id:
                            logger.info(f"Matched response for {correlation_id}")
                            responses.append(response)

                            # Session deletion responses are always final
                            if "deleted_at" in response or response.get("session_id"):
                                logger.info("Received final response")
                                break

                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to decode message: {e}")
                        continue

                except asyncio.TimeoutError:
                    logger.warning("Timeout waiting for message")
                    break
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    continue

            logger.info(f"Collected {len(responses)} responses")
            return responses

        except Exception as e:
            logger.error(f"Error in listen_for_responses: {e}")
            raise


async def create_test_session(client: SessionDeletionTestClient):
    """Create a test session for deletion testing"""
    print("\n🔧 Creating test session for deletion")
    print("-" * 40)

    try:
        run_id = str(uuid.uuid4())
        creation_request = {
            "agent_id": "d406f37f-5c8f-43ce-8835-a69a9a8a764a",
            "user_id": "test_user",
            "communication_type": "single",
            "run_id": run_id,
            "agent_group_id": None,
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        await client.send_message(KAFKA_AGENT_CREATION_TOPIC, creation_request, headers)

        responses = await client.listen_for_responses(correlation_id=run_id, timeout=60)

        if responses and responses[0].get("session_id"):
            session_id = responses[0]["session_id"]
            print(f"✅ Test session created successfully")
            print(f"   Session ID: {session_id}")
            return session_id
        else:
            print("❌ Failed to create test session")
            return None

    except Exception as e:
        print(f"❌ Session creation error: {e}")
        return None


async def test_session_deletion(client: SessionDeletionTestClient, session_id: str):
    """Test session deletion functionality"""
    print(f"\n🗑️  Testing Session Deletion")
    print("-" * 40)

    try:
        run_id = str(uuid.uuid4())
        deletion_request = {
            "session_id": session_id,
            "run_id": run_id,
            "user_id": "test_user",
            "reason": "chat_complete",
            "force": False,
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        await client.send_message(
            KAFKA_AGENT_SESSION_DELETION_TOPIC, deletion_request, headers
        )

        responses = await client.listen_for_responses(correlation_id=run_id, timeout=30)

        if responses:
            for response in responses:
                if response.get("success", False):
                    deleted_at = response.get("deleted_at", "Unknown")
                    message = response.get("message", "No message")
                    print(f"✅ Session deleted successfully")
                    print(f"   Session ID: {response.get('session_id')}")
                    print(f"   Deleted at: {deleted_at}")
                    print(f"   Message: {message}")
                    return True
                else:
                    error_msg = response.get("message", "Unknown error")
                    print(f"❌ Deletion failed: {error_msg}")
                    return False
        else:
            print("❌ No deletion response received")
            return False

    except Exception as e:
        print(f"❌ Deletion error: {e}")
        return False


async def test_force_deletion(client: SessionDeletionTestClient):
    """Test force deletion of non-existent session"""
    print(f"\n💪 Testing Force Deletion")
    print("-" * 40)

    try:
        # Use a fake session ID
        fake_session_id = str(uuid.uuid4())
        run_id = str(uuid.uuid4())

        deletion_request = {
            "session_id": fake_session_id,
            "run_id": run_id,
            "user_id": "test_user",
            "reason": "force_cleanup",
            "force": True,
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        await client.send_message(
            KAFKA_AGENT_SESSION_DELETION_TOPIC, deletion_request, headers
        )

        responses = await client.listen_for_responses(correlation_id=run_id, timeout=30)

        if responses:
            for response in responses:
                if response.get("success", False):
                    print(f"✅ Force deletion successful")
                    print(f"   Session ID: {response.get('session_id')}")
                    print(f"   Message: {response.get('message')}")
                    return True
                else:
                    error_msg = response.get("message", "Unknown error")
                    print(f"❌ Force deletion failed: {error_msg}")
                    return False
        else:
            print("❌ No force deletion response received")
            return False

    except Exception as e:
        print(f"❌ Force deletion error: {e}")
        return False


async def test_deletion_without_force(client: SessionDeletionTestClient):
    """Test deletion of non-existent session without force"""
    print(f"\n🚫 Testing Deletion Without Force (Should Fail)")
    print("-" * 40)

    try:
        # Use a fake session ID
        fake_session_id = str(uuid.uuid4())
        run_id = str(uuid.uuid4())

        deletion_request = {
            "session_id": fake_session_id,
            "run_id": run_id,
            "user_id": "test_user",
            "reason": "test_failure",
            "force": False,
        }

        headers = [
            ("correlationId", run_id.encode("utf-8")),
            ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
        ]

        await client.send_message(
            KAFKA_AGENT_SESSION_DELETION_TOPIC, deletion_request, headers
        )

        responses = await client.listen_for_responses(correlation_id=run_id, timeout=30)

        if responses:
            for response in responses:
                if not response.get("success", True):
                    error_msg = response.get("message", "Unknown error")
                    print(f"✅ Expected failure occurred: {error_msg}")
                    return True
                else:
                    print(f"❌ Unexpected success - should have failed")
                    return False
        else:
            print("❌ No response received")
            return False

    except Exception as e:
        print(f"❌ Test error: {e}")
        return False


async def run_comprehensive_deletion_test():
    """Run comprehensive session deletion tests"""
    client = SessionDeletionTestClient()
    await client.initialize()

    try:
        print("🚀 Starting Session Deletion Test Suite")
        print("=" * 50)

        results = {
            "session_creation": False,
            "session_deletion": False,
            "force_deletion": False,
            "expected_failure": False,
        }

        # Test 1: Create a session
        session_id = await create_test_session(client)
        results["session_creation"] = session_id is not None

        # Test 2: Delete the created session
        if session_id:
            results["session_deletion"] = await test_session_deletion(
                client, session_id
            )

        # Test 3: Force deletion of non-existent session
        results["force_deletion"] = await test_force_deletion(client)

        # Test 4: Deletion without force (should fail)
        results["expected_failure"] = await test_deletion_without_force(client)

        # Print summary
        print("\n📊 Test Results Summary")
        print("=" * 50)

        passed = sum(results.values())
        total = len(results)

        for test_name, passed_test in results.items():
            status = "✅ PASSED" if passed_test else "❌ FAILED"
            print(f"{test_name.replace('_', ' ').title()}: {status}")

        print(f"\nOverall: {passed}/{total} tests passed")

        if passed == total:
            print("🎉 All deletion tests passed!")
        else:
            print("⚠️  Some tests failed. Check the logs for details.")

        return results

    except Exception as e:
        logger.error(f"Comprehensive test error: {e}")
        print(f"❌ Test suite failed: {e}")
        return None
    finally:
        await client.cleanup()


async def interactive_session_deletion():
    """Interactive session deletion interface"""
    client = SessionDeletionTestClient()
    await client.initialize()

    try:
        print("\nInteractive Session Deletion")
        print("=" * 50)

        while True:
            print("\nOptions:")
            print("1. Delete existing session")
            print("2. Force delete session")
            print("3. Create and delete session")
            print("4. Exit")

            choice = input("\nSelect option (1-4): ").strip()

            if choice == "1":
                session_id = input("Enter session ID to delete: ").strip()
                if session_id:
                    await test_session_deletion(client, session_id)

            elif choice == "2":
                session_id = input("Enter session ID to force delete: ").strip()
                if not session_id:
                    session_id = str(uuid.uuid4())
                    print(f"Using fake session ID: {session_id}")

                run_id = str(uuid.uuid4())
                deletion_request = {
                    "session_id": session_id,
                    "run_id": run_id,
                    "user_id": "test_user",
                    "reason": "manual_force_delete",
                    "force": True,
                }

                headers = [
                    ("correlationId", run_id.encode("utf-8")),
                    ("reply-topic", KAFKA_AGENT_RESPONSE_TOPIC.encode("utf-8")),
                ]

                await client.send_message(
                    KAFKA_AGENT_SESSION_DELETION_TOPIC, deletion_request, headers
                )

                responses = await client.listen_for_responses(
                    correlation_id=run_id, timeout=30
                )

                if responses:
                    for response in responses:
                        success = response.get("success", False)
                        message = response.get("message", "No message")
                        status = "✅" if success else "❌"
                        print(f"{status} {message}")

            elif choice == "3":
                session_id = await create_test_session(client)
                if session_id:
                    await test_session_deletion(client, session_id)

            elif choice == "4":
                break

            else:
                print("Invalid choice. Please select 1-4.")

    except Exception as e:
        logger.error(f"Interactive deletion error: {e}")
        print(f"❌ Error: {e}")
    finally:
        await client.cleanup()


async def main():
    try:
        print("Session Deletion Test Options:")
        print("1. Comprehensive test suite")
        print("2. Interactive deletion interface")

        choice = input("\nSelect option (1 or 2): ").strip()

        if choice == "1":
            await run_comprehensive_deletion_test()
        elif choice == "2":
            await interactive_session_deletion()
        else:
            print("Invalid choice. Running comprehensive test...")
            await run_comprehensive_deletion_test()

    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
