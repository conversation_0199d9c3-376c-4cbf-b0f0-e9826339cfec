### Python ###
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/
coverage_html/
.roo

# Translations
*.mo
*.pot

# Django stuff
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff
instance/
.webassets-cache

# Scrapy stuff
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# PEP 582; used by e.g. github.com/David-O<PERSON>onnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

### IDEs/Editors ###
# VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# PyCharm
.idea/
*.iml
*.iws
.idea_modules/

# Sublime Text
*.tmlanguage.cache
*.tmPreferences.cache
*.stTheme.cache
*.sublime-workspace
*.sublime-project

### Operating System ###
# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

### Project Specific ###
# Proto definitions and generated files
proto-definitions/
app/grpc/

# Testing directories
testing/

# Temporary files
*.swp
*.swo
*~

# Local development files
chat_client.py
chat_service.py
generate_grpc.py

# Logs
logs/
*.log
log.txt

# Database
*.db
*.sqlite

# Configuration
config.local.yaml
.env.local
.env.*.local

# Dependencies
poetry.lock

# Build output
dist/
build/

# Cache directories
.cache/
.pytest_cache/

# Local development environment
.local/

# Docker volumes
docker-volumes/

# Temporary files
tmp/
temp/

# Generated documentation
docs/_build/
docs/api/

# Security/Secrets
*.pem
*.key
*.cert
secrets/
.secrets/

# Backup files
*.bak
*.backup
*~

# ChromaDB files
.chromadb_autogen/
.chroma/

# roo
.roo/

# Kafka data
kafka-data/

# Redis data
redis-data/

# Model cache
.model_cache/

# Kafka data
memory-bank/

docs/
