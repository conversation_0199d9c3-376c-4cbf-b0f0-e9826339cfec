apiVersion: v1
kind: ServiceAccount
metadata:
  name: agent-platform-service-ai-sa
  namespace: ruh-catalyst
  labels:
    name: agent-platform-service-ai-sa
    namespace: ruh-catalyst
    app: agent-platform-service-ai
    deployment: agent-platform-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-platform-service-ai-dp
  namespace: ruh-catalyst
  labels:
    name: agent-platform-service-ai-dp
    namespace: ruh-catalyst
    app: agent-platform-service-ai
    serviceaccount: agent-platform-service-ai-sa
    deployment: agent-platform-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: agent-platform-service-ai
      deployment: agent-platform-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-catalyst
        app: agent-platform-service-ai
        deployment: agent-platform-service-ai-dp
    spec:
      serviceAccountName: agent-platform-service-ai-sa      
      containers:
      - name: agent-platform-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 6000
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: agent-platform-service-ai-svc
  namespace: ruh-catalyst
spec:
  selector:
    app: agent-platform-service-ai
    deployment: agent-platform-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 6000
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
## Create HPA
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: agent-platform-service-hpa
  namespace: ruh-catalyst
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: agent-platform-service-ai-dp
  minReplicas: 1
  maxReplicas: 5
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
---
### Create Nginx Ingress
# apiVersion: networking.k8s.io/v1
# kind: Ingress
# metadata:
#   name: agent-platform-service-ingress
#   namespace: ruh-catalyst
# spec:
#   ingressClassName: nginx
#   rules:
#   - host: agent-platform-service-dev.rapidinnovation.dev
#     http:
#       paths:
#       - path: /
#         pathType: Prefix
#         backend:
#           service:
#             name: agent-platform-service-ai-svc
#             port:
#               number: 80

