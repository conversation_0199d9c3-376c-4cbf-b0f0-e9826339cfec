# Agent Platform

A modular Python service for creating and managing AI agent sessions with dynamic tool loading, knowledge retrieval, and streaming responses. The platform supports agent-to-agent communication, workflow integration, and comprehensive knowledge management capabilities.

## Project Structure

```
agent-platform/
├── app/                    # Application code
│   ├── autogen_service/    # AutoGen integration
│   │   ├── agent_factory.py       # Agent creation and management
│   │   ├── agent_config_parser.py # Agent configuration parsing
│   │   ├── head_agent.py          # Head agent implementation
│   │   ├── ai_agent.py            # Base AI agent classes
│   │   ├── chat_processor.py      # Chat session processing
│   │   ├── group_chat_factory.py  # Group chat creation
│   │   ├── group_chat_processor.py # Group chat management
│   │   ├── model_factory.py       # Model client creation
│   │   ├── user_agent.py          # User agent implementation
│   │   ├── autogen_mcp.py         # MCP tool integration
│   │   └── agent_fetching.py      # Agent configuration fetching
│   ├── cli/                # Command line interface
│   │   └── chat_cli.py     # CLI chat interface
│   ├── examples/           # Usage examples
│   │   ├── logging_example.py     # Logging system examples
│   │   └── workflow_tool_example.py # Workflow tool examples
│   ├── executer/           # Service execution
│   │   └── run.py          # Service runner
│   ├── helper/             # Helper utilities
│   │   ├── api_call.py     # HTTP request helper
│   │   ├── redis_client.py # Redis client wrapper
│   │   └── session_manager.py # Session management
│   ├── kafka_client/       # Kafka integration
│   │   ├── consumer.py     # Kafka consumer
│   │   └── producer.py     # Kafka producer
│   ├── knowledge/          # Knowledge management
│   │   ├── knowledge_manager.py   # Knowledge retrieval and storage
│   │   └── knowledge_example.py   # Example usage
│   ├── memory/             # Memory management
│   │   ├── pinecone_memory_manager.py # Pinecone integration
│   │   └── pinecone_memory.py         # Pinecone memory implementation
│   ├── schemas/            # Data models
│   │   ├── agent_config.py # Agent configuration models
│   │   ├── api.py          # API models
│   │   ├── kafka.py        # Kafka message models
│   │   └── models.py       # Core Pydantic models
│   ├── services/           # Service layer
│   │   ├── agent_chat.py   # Agent chat service
│   │   ├── agent_fetch.py  # Agent fetching service
│   │   ├── agent_group_service.py # Group chat service
│   │   ├── knowledge_service.py   # Knowledge management service
│   │   └── mcp_service.py  # MCP service integration
│   ├── shared/             # Shared components
│   │   ├── config/         # Configuration management
│   │   │   ├── base.py     # Base settings
│   │   │   ├── environments.py    # Environment configurations
│   │   │   └── logging_config.py  # Logging setup
│   │   ├── session/        # Session management
│   │   │   └── session_manager.py # Session handling
│   │   └── executable_workflow.py # Workflow execution
│   ├── tools/              # Tool implementations
│   │   ├── tool_loader.py          # Base tool loading
│   │   ├── dynamic_tool_loader.py  # Dynamic API tool loading
│   │   ├── dynamic_tool.py         # Dynamic tool implementation
│   │   ├── workflow_tool_loader.py # Workflow tool loading
│   │   ├── mcp_tool_loader.py      # MCP tool loading
│   │   ├── knowledge_tool_loader.py # Knowledge tool loading
│   │   └── streaming_function_tool.py # Streaming tool support
│   └── main.py             # Application entry point
├── config/                 # Configuration files
│   ├── agents.json         # Agent configurations
│   └── agents.py           # Agent configuration helpers
├── docs/                   # Documentation
│   ├── group_chat_implementation.md    # Group chat documentation
│   ├── knowledge_tools_implementation.md # Knowledge tools documentation
│   └── pinecone_memory_implementation.md # Memory implementation docs
├── memory-bank/            # Project memory and context
│   ├── productContext.md   # Project overview and goals
│   ├── activeContext.md    # Current status and focus
│   ├── progress.md         # Task progress tracking
│   ├── decisionLog.md      # Architectural decisions
│   └── systemPatterns.md   # Code and design patterns
├── scripts/                # Utility scripts
│   ├── run_tests.sh        # Test runner
│   ├── initialize_pinecone_memory.py # Memory initialization
│   ├── verify_memory_fix.py # Memory verification
│   └── integration/        # Integration test scripts
│       └── run_kafka_test.py # Kafka integration test
├── tests/                  # Test suite
│   ├── agents/             # Agent tests
│   ├── api/                # API tests
│   ├── shared/             # Shared component tests
│   ├── tools/              # Tool tests
│   ├── test_chat_flow.py   # Chat flow tests
│   ├── test_group_chat_flow.py # Group chat tests
│   └── test_pinecone_memory_*.py # Memory tests
├── .env.example            # Environment variable template
├── .coveragerc             # Coverage configuration
├── .dockerignore           # Docker ignore file
├── .gitignore              # Git ignore file
├── Dockerfile              # Docker configuration
├── pyproject.toml          # Poetry configuration
├── TASK_LIST.md            # Development task tracking
├── TRD.md                  # Technical requirements document
└── README.md               # This file
```

## Features

- **Dynamic Agent Creation**: Create and configure agents on-the-fly with flexible JSON configuration
- **Multi-Agent Communication**: Head Agent delegation system with intelligent task routing
- **Tool Integration**: Support for various tool types (MCP, Workflow, Function, API tools)
- **Streaming Responses**: Real-time streaming of agent responses via Server-Sent Events
- **Kafka Integration**: Asynchronous message processing for scalable communication
- **Optimized Session Management**: Redis-based session persistence with zlib compression (50-80% size reduction)
- **Comprehensive Logging**: Structured logging with multiple levels, JSON formatting, and component-specific configuration
- **MCP Tool Integration**: Support for Machine Control Program tools with dynamic loading
- **Streaming Tool Support**: Real-time streaming of tool execution results
- **Knowledge Retrieval**: RAG capabilities with ChromaDB and Pinecone vector storage
- **Agent-to-Agent Communication**: Structured messaging system for inter-agent collaboration
- **Head Agent Delegation**: Dynamic task delegation to specialized agents
- **Group Chat Support**: Multi-agent group conversations with configurable termination conditions
- **Memory Management**: Pinecone integration for advanced vector storage and retrieval
- **Configuration Reloading**: Runtime configuration updates without service restart

## Installation

1. **Clone the Repository:**

   ```bash
   git clone https://gitlab.rapidinnovation.tech/ruh-catalyst/agent-platform.git
   cd agent-platform
   ```

2. **Set Up Environment:**

   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Install Dependencies:**

   ```bash
   poetry install
   ```

4. **Run the Application:**

   ```bash
   # Run in server mode (Kafka consumer)
   poetry run python -m app.main --mode server

   # Run in engine mode (Service runner)
   poetry run python -m app.main --mode engine
   ```

5. **Using Docker:**

   ```bash
   # Build the Docker image
   docker build -t agent-platform .

   # Run the container
   docker run -p 6000:6000 --env-file .env agent-platform

   # Run with volume for persistent data storage
   docker run -p 6000:6000 -v $(pwd)/data:/app/data --env-file .env agent-platform
   ```

## Architecture

### Core Components

- **Agent Factory**: Central component for creating and managing agent instances
- **Head Agent**: Intelligent delegation system that routes tasks to specialized agents
- **Chat Processor**: Handles chat sessions and message processing
- **Group Chat Factory**: Creates and manages multi-agent group conversations
- **Tool Loaders**: Modular system for loading different types of tools (MCP, Workflow, Dynamic, Knowledge)
- **Knowledge Manager**: RAG implementation with vector storage integration
- **Session Manager**: Redis-based session persistence with compression

### Agent Types

1. **Head Agent**: Central coordinator that delegates tasks to specialized agents
2. **Marketing Agent**: Specialized for marketing content creation with video generation and blog automation tools
3. **Sales Agent**: Focused on sales-related tasks and customer interaction
4. **AI Agent**: General-purpose agent with customizable tools and capabilities

### Tool System

The platform supports multiple tool types through a modular loading system:

1. **MCP Tools**: Machine Control Program tools for external service integration
2. **Workflow Tools**: Execute workflows in external workflow systems
3. **Dynamic Tools**: API-based tools with JSON payload support
4. **Knowledge Tools**: RAG-enabled tools for knowledge retrieval
5. **Function Tools**: Direct Python function execution
6. **Streaming Tools**: Real-time streaming of tool execution results

### Session Management Optimizations

The platform includes optimized session management with:

1. **Memory Compression**: Uses zlib compression to reduce storage size by 50-80%
2. **Message Window Management**: Maintains a configurable window of recent messages
3. **Selective Configuration Storage**: Stores only essential agent configuration data
4. **Binary Storage**: Efficient binary data handling for compressed messages
5. **Efficient Session Listing**: Uses Redis SCAN for better performance

Configuration options:

```python
session_manager = SessionManager(
    redis_client,
    session_ttl=3600,        # 1 hour TTL
    max_messages=50,         # Keep only 50 messages per session
    compression_level=6      # Compression level (0-9)
)
```

## Development

### Adding New Agents

1. Create agent configuration in `config/agents.json`
2. Implement agent class extending `AIAgent` or `RoutedAgent`
3. Register agent in the agent factory
4. Add agent-specific tools and workflows

### Creating Custom Tools

1. Define tool schema and configuration
2. Implement tool logic in appropriate tool loader:
   - `DynamicToolLoader` for API tools
   - `WorkflowToolLoader` for workflow tools
   - `McpToolLoader` for MCP tools
   - `KnowledgeToolLoader` for knowledge tools

### Knowledge Management

Add knowledge to agents using the Knowledge Service:

```python
from app.services.knowledge_service import KnowledgeService

knowledge_service = KnowledgeService()

await knowledge_service.add_knowledge_to_agent(
    agent_id="agent_name",
    agent=agent_instance,
    knowledge_sources={
        "texts": ["Knowledge content"],
        "urls": ["https://example.com"],
        "documents": ["/path/to/document.pdf"]
    }
)
```

### Configuration Management

The application uses environment variables for configuration:

- API settings and model credentials
- Kafka and Redis connection details
- Workflow API endpoints
- Agent configuration parameters
- Logging configuration

Runtime configuration reloading:

```bash
curl http://localhost:6000/reload-config
```

## Testing

### Running Tests

1. **Using the test script:**

   ```bash
   ./scripts/run_tests.sh
   ```

2. **Running directly with pytest:**

   ```bash
   PYTHONPATH=$PYTHONPATH:. poetry run pytest
   ```

3. **With coverage report:**

   ```bash
   PYTHONPATH=$PYTHONPATH:. poetry run pytest --cov=app --cov-report=html --cov-report=term-missing
   ```

### Test Coverage

- HTML report generated in `coverage_html/` directory
- Comprehensive test suite covering agents, tools, services, and integrations
- Performance and stress testing included

## Logging

The application uses a comprehensive structured logging system:

### Configuration

```bash
# Enable multiple log levels
LOG_LEVEL=DEBUG,INFO,WARNING,ERROR,CRITICAL

# Set component-specific log levels
KAFKA_LOG_LEVEL=DEBUG
AGENT_LOG_LEVEL=INFO
SESSION_LOG_LEVEL=WARNING
REDIS_LOG_LEVEL=ERROR

# Enable JSON formatting
LOG_FORMAT=json

# Set log directory
LOGS_DIR=custom_logs
```

### Usage

```python
from app.shared.config.logging_config import get_logger, log_with_context

logger = get_logger(__name__)
logger.info("Application started")

# Log with context
log_with_context(
    logger,
    logging.INFO,
    "User action",
    extra={"user_id": "123", "session_id": "456"}
)
```

## Memory Bank

The project includes a comprehensive Memory Bank system for maintaining project context:

- **productContext.md**: Project overview, goals, and architecture
- **activeContext.md**: Current status, recent changes, and open questions
- **progress.md**: Task progress tracking and development priorities
- **decisionLog.md**: Architectural decisions with rationale
- **systemPatterns.md**: Code patterns and design standards

## API Documentation

When running, access API documentation at:

- Swagger UI: http://localhost:6000/docs
- ReDoc: http://localhost:6000/redoc

## Contributing

1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Update documentation
5. Submit pull request

## License

[Specify License]

## Acknowledgements

- Managed using [Poetry](https://python-poetry.org/)
- Uses [Microsoft AutoGen](https://github.com/microsoft/autogen) for agent framework
- Integrates with [Kafka](https://kafka.apache.org/) for message processing
- Uses [Redis](https://redis.io/) for session management
- Uses [ChromaDB](https://www.trychroma.com/) and [Pinecone](https://www.pinecone.io/) for vector storage
- Integrates with [Anthropic Claude](https://www.anthropic.com/) and [OpenAI](https://openai.com/) models
