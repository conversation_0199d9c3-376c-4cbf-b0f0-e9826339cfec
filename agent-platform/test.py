import asyncio
from typing import As<PERSON><PERSON>enerator, Sequence
from autogen_agentchat.agents import BaseChatAgent
from autogen_agentchat.base import Response
from autogen_agentchat.messages import BaseChatMessage, TextMessage
from autogen_core import CancellationToken


# Streaming counter tool
async def counter_tool(start: int) -> AsyncGenerator[int, None]:
    for i in range(start, -1, -1):
        await asyncio.sleep(5)
        yield i


class CounterAgent(BaseChatAgent):
    def __init__(self, name: str):
        super().__init__(name, description="Streams a countdown")
        self._start = 0

    @property
    def produced_message_types(self) -> Sequence[type[BaseChatMessage]]:
        return (TextMessage,)

    async def on_messages(
        self,
        messages: Sequence[BaseChatMessage],
        cancellation_token: CancellationToken,
    ) -> Response:
        resp = None
        async for evt in self.on_messages_stream(messages, cancellation_token):
            if isinstance(evt, Response):
                resp = evt
        assert resp is not None
        return resp

    async def on_messages_stream(
        self,
        messages: Sequence[BaseChatMessage],
        cancellation_token: CancellationToken,
    ) -> AsyncGenerator[BaseChatMessage | Response, None]:
        text = messages[-1].content.strip() if messages else ""
        try:
            self._start = int(text)
        except ValueError:
            yield TextMessage(
                content="✋ Send a number to start the countdown.", source=self.name
            )
            return

        inner = []
        async for v in counter_tool(self._start):
            msg = TextMessage(content=f"Count: {v}", source=self.name)
            inner.append(msg)
            yield msg
            if cancellation_token.is_cancelled:
                break

        final = TextMessage(content="🎉 Countdown complete!", source=self.name)
        yield Response(chat_message=final, inner_messages=inner)

    async def on_reset(self, cancellation_token: CancellationToken) -> None:
        """Reset internal state if needed."""
        self._start = 0


# Runner using run_stream
async def main():
    agent = CounterAgent("counter")
    token = CancellationToken()
    # Trigger agent with "5"
    async for pkt in agent.run_stream(task="5", cancellation_token=token):
        if isinstance(pkt, Response):
            print("Final:", pkt.chat_message.content)
        else:
            # Could be TextMessage or streaming chunk
            content = pkt.content if hasattr(pkt, "content") else str(pkt)
            print(content)


if __name__ == "__main__":
    asyncio.run(main())
