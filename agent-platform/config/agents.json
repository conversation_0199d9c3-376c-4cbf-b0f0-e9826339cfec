{"agent_1": {"id": "marketing_specialist_user1", "name": "marketing_specialist", "description": "Marketing Content Specialist Agent for User 1", "avatar": "", "owner_id": "", "user_ids": [], "owner_type": "", "template_id": "", "parent_agent_id": "", "url": "", "is_imported": false, "agent_category": "AIAgent", "system_message": "You are a marketing content assistant. How to use the tools: 1. **Extract Parameters:** Based *only* on the description of the *specific tool you selected* (like the one above), identify the necessary input parameter *keys* and extract their *values* from the user's request.", "model_provider": "OpenAIChatCompletionClient", "model_name": "gpt-4o", "model_api_key": "os.getenv(\"OPENAI_API_KEY\")", "workflow_ids": [{"id": "456173c8-9547-4800-b171-5527df3f6f89", "name": "video_generation", "description": "Generates a video based on the provided topic, script type and video type.", "parameters": {"type": "object", "properties": {"topic": {"type": "string", "description": "The main subject of the video."}, "video_type": {"type": "string", "description": "The type of video to be generated."}, "script_type": {"type": "string", "description": "The type of script to be generated."}, "keywords": {"type": "string", "description": "Relevant keywords for the video content."}, "view_type": {"type": "string", "description": "The view type for the video."}, "avatar_id": {"type": "string", "description": "The ID of the avatar to be used."}, "voice_id": {"type": "string", "description": "The ID of the voice to be used."}, "caption": {"type": "string", "description": "The caption for the video."}, "link": {"type": "string", "description": "A link to be included in the video."}, "event_stock_clips": {"type": "array", "description": "A list of event stock clips to be used in the video.", "items": {"type": "string"}}}, "required": ["topic", "video_type", "script_type", "view_type"]}}], "mcp_server_ids": [], "agent_topic_type": "MarketingAgentUser1", "subscriptions": "pro", "visibility": "", "category": "", "tags": {}, "status": "", "created_at": "", "updated_at": "", "agent_tools": [{"tool_type": "workflow", "url": "http://localhost:5000/execute-by-name", "workflow": {"workflow_id": "data_processing_test_workflow", "approval": "False", "description": "Generate a script, audio, and images for a marketing video", "payload": {"user_dependent_fields": ["topic", "video_type", "keywords"], "user_payload_template": {"topic": "", "video_type": "", "keywords": ""}}}}, {"tool_type": "workflow", "url": "http://localhost:5002/execute-by-name", "workflow": {"workflow_id": "blog_automation", "description": "Generates a blog post for a given topic. MUST be called with a 'parameters' dictionary containing exactly these keys: 'topic' (string: the main subject) and 'keywords' (string: relevant terms).", "payload": {"user_dependent_fields": ["topic"], "user_payload_template": {"topic": ""}}}}]}, "agent_2": {"id": "sales_agent_user2", "name": "sales_agent", "description": "Sales Agent for User 2", "avatar": "", "owner_id": "", "user_ids": [], "owner_type": "", "template_id": "", "parent_agent_id": "", "url": "", "is_imported": false, "agent_category": "AIAgent", "system_message": "You are a sales agent...", "model_provider": "OpenAIChatCompletionClient", "model_name": "gpt-4o-mini", "model_api_key": "", "workflow_ids": [], "mcp_server_ids": [], "agent_topic_type": "SalesAgentUser2", "subscriptions": [{"topic_type": "SalesAgentUser2"}], "visibility": "", "category": "", "tags": {}, "status": "", "created_at": "", "updated_at": "", "agent_tools": []}, "agent_3": {"id": "head_agent", "name": "head_agent", "description": "Central Head Agent for routing user requests", "avatar": "", "owner_id": "", "user_ids": [], "owner_type": "", "template_id": "", "parent_agent_id": "", "url": "", "is_imported": false, "agent_category": "HeadAgent", "system_message": "You are the company head agent. **When calling the tools, ALWAYS include the user's original message as the value for the `user_message` parameter.** Example: User message: \"I want to generate a marketing video script.\" Tool Call: { \"name\": \"tool_name\", \"arguments\": { \"user_message\": \"\" } } Do not respond with text, always delegate to the task.", "model_provider": "OpenAIChatCompletionClient", "model_name": "gpt-4o-mini", "model_api_key": "", "workflow_ids": [], "mcp_server_ids": [], "agent_topic_type": "HeadAgent", "subscriptions": [{"topic_type": "HeadAgent"}], "visibility": "", "category": "", "tags": {}, "status": "", "created_at": "", "updated_at": "", "agent_tools": []}}