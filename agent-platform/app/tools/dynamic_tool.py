from pydantic import create_model, BaseModel
from autogen_core.tools import BaseTool
from autogen_core import CancellationToken
import httpx
import time
import asyncio

# Map JSON Schema types to Python types
json_type_to_py = {
    "string": str,
    "integer": int,
    "number": float,
    "boolean": bool,
    "object": dict,
    "array": list,
}


def create_dynamic_tool(name, description, json_schema, api_url, method="POST"):
    # Build fields dict with correct Python types
    fields = {}
    for k, v in json_schema.get("properties", {}).items():
        py_type = json_type_to_py.get(v.get("type", "string"), str)
        default = ... if k in json_schema.get("required", []) else None
        fields[k] = (py_type, default)
    ArgsModel = create_model(f"{name}_Args", **fields)
    ArgsModel.model_rebuild()  # Ensure the model is fully defined

    class DynamicTool(BaseTool[ArgsModel, BaseModel]):
        def __init__(self):
            super().__init__(
                args_type=ArgsModel,
                return_type=BaseModel,
                name=name,
                description=description,
            )

        async def run(self, args: ArgsModel, cancellation_token: CancellationToken):
            payload = args.dict(exclude_none=True)

            print(f"Payload: {payload}")

            # Simulate streaming a response in chunks
            async def example_response_stream():
                chunks = [
                    '{"city": "Delhi", ',
                    '"temperature": 33.5, ',
                    '"weather": "partly cloudy", ',
                    '"humidity": 44, ',
                    '"wind_speed": 3.6}',
                    '{"city": "mumbai", ',
                    '"temperature": 20.5, ',
                    '"weather": "rainy cloudy", ',
                    '"humidity": 44, ',
                    '"wind_speed": 3.6}',
                ]
                for chunk in chunks:
                    await asyncio.sleep(1)  # Simulate delay between chunks
                    yield chunk

            return example_response_stream()

            # async with httpx.AsyncClient() as client:
            #     async with client.stream(method, api_url, json=payload) as response:
            #         response.raise_for_status()
            #         async for chunk in response.aiter_text():
            #             # Yield each chunk as it arrives
            #             yield chunk

            # async with httpx.AsyncClient() as client:
            #     resp = await client.request(method, api_url, json=payload)
            #     resp.raise_for_status()
            #     return resp.json()

        # def return_value_as_string(self, value):
        #     return str(value)

        def return_value_as_string(self, value):
            print(f"Value: {value}")
            # If value is an async generator, raise a clear error or return a placeholder.
            if hasattr(value, "__aiter__"):
                # Streaming result: cannot join in sync context.
                return (
                    "[STREAMING TOOL RESULT: consume this value as an async generator. "
                    "Use await tool.join_stream(value) to get the full result as a string.]"
                )
            return str(value)

        async def join_stream(self, value):
            # Helper to join all chunks from an async generator
            if hasattr(value, "__aiter__"):
                return "".join([chunk async for chunk in value])
            return str(value)

    return DynamicTool()


# Example usage:
json_schema = {
    "type": "object",
    "properties": {
        "city": {"type": "string", "description": "City name"},
        "name": {
            "type": "string",
            "description": "name of the person",
            "format": "date",
        },
        "date": {
            "type": "string",
            "description": "Date (YYYY-MM-DD)",
            "format": "date",
        },
    },
    "required": ["city", "name"],
}
api_url = "https://api.example.com/weather"
weather_tool = create_dynamic_tool(
    name="get_weather",
    description="Get the weather for a city on a given date.",
    json_schema=json_schema,
    api_url=api_url,
    method="POST",
)
