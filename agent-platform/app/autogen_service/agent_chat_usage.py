import asyncio
from src.services.agent_chat import AgentChat


async def main():
    # Create chat instance
    agent_chat = AgentChat()

    # Register a new agent
    agent = await agent_chat.register_agent(
        name="PersonalAssistant",
        agent_type="autogen",
        description="A helpful personal assistant",
        configuration={
            "model": "gpt-4",
            "temperature": 0.7,
            "max_tokens": 150,
            "personality": "helpful and friendly",
        },
    )
    print(f"Registered agent: {agent.name} (ID: {agent.id})")

    # Start a chat session
    session = await agent_chat.start_chat_session()
    print(f"Started chat session: {session.session_id}")

    try:
        # Example conversation
        messages = [
            "Hello! Can you help me with some tasks?",
            "What can you tell me about Python programming?",
            "How can I handle exceptions in Python?",
        ]

        for message in messages:
            print(f"\nUser: {message}")
            response = await agent_chat.send_message(message)
            print(f"Assistant: {response.content}")

        # Get chat history
        print("\nChat History:")
        history = await agent_chat.get_chat_history()
        for msg in history:
            print(f"{msg.role}: {msg.content}")

    finally:
        # End chat session
        await agent_chat.end_chat_session()
        print("\nChat session ended")


if __name__ == "__main__":
    asyncio.run(main())
