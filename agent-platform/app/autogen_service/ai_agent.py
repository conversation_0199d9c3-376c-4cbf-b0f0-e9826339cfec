# agents/handoff_agent.py
import asyncio
import json
from typing import List, <PERSON><PERSON>, Dict, Async<PERSON>enerator, Any, Optional
from autogen_core import AgentType
import logging
from ..schemas.models import UserTask, AgentResponse
from autogen_core import (
    FunctionCall,
    MessageContext,
    RoutedAgent,
    TopicId,
    message_handler,
)
from autogen_core.models import (
    AssistantMessage,
    ChatCompletionClient,
    FunctionExecutionResult,
    FunctionExecutionResultMessage,
    SystemMessage,
)
from autogen_core.tools import Tool
from ..tools.streaming_function_tool import StreamingFunctionTool
from datetime import datetime

logger = logging.getLogger(__name__)


class AIAgent(RoutedAgent):
    """
    A specialized agent designed to handle tasks and delegate subtasks to other agents or tools.

    This agent uses a language model to process user tasks, decide on the appropriate action,
    and either execute a tool or delegate the task to another agent.
    """

    def __init__(
        self,
        description: str,
        system_message: SystemMessage,
        model_client: ChatCompletionClient,
        tools: List[Tool],
        delegate_tools: List[Tool],
        agent_topic_type: str,
        user_topic_type: str,
        registered_agent_types: Dict[str, AgentType] = None,
    ) -> None:
        """
        Initializes the AIAgent with its core components.

        Args:
            description (str): A brief description of the agent's role.
            system_message (SystemMessage): The system message guiding the agent's behavior.
            model_client (ChatCompletionClient): The language model client for generating responses.
            tools (List[Tool]): A list of tools available to the agent.
            delegate_tools (List[Tool]): A list of tools for delegating tasks to other agents.
            agent_topic_type (str): The topic type for messages this agent handles.
            user_topic_type (str): The topic type for user-related messages.
            registered_agent_types (Dict[str, AgentType], optional): A dictionary of registered agent types. Defaults to None.
        """
        super().__init__(description)
        self._system_message = system_message
        self._model_client = model_client
        # At this point, we assume that tools and delegate_tools are fully initialized Tool objects.
        # Their asynchronous creation should be handled before passing them to this constructor.
        self._tools = {tool.name: tool for tool in tools}
        self._tool_schema = [tool.schema for tool in tools]
        self._delegate_tools = {tool.name: tool for tool in delegate_tools}
        self._delegate_tool_schema = [tool.schema for tool in delegate_tools]
        self._agent_topic_type = agent_topic_type
        self._user_topic_type = user_topic_type
        self.registered_agent_types = registered_agent_types or {}

    async def use_tool(
        self, tool_name: str, parameters: Dict[str, Any], cancellation_token: Any
    ) -> Optional[AsyncGenerator[str, None]]:
        """
        Helper method to call a tool (assumed streaming) by name.
        Returns the asynchronous generator from the tool call.
        """
        tool = self._tools.get(tool_name)
        if not tool:
            raise ValueError(f"Tool not found: {tool_name}")
        # Call tool.run_json which should return an async generator for streaming tools.
        try:

            async_gen = await tool.run_json(parameters, cancellation_token)
            return async_gen
        except Exception as e:
            # Log the error and re-raise if needed.

            raise

    @message_handler
    async def handle_task(self, message: UserTask, ctx: MessageContext) -> None:
        """
        Handles an incoming user task by processing it with the language model and executing or delegating as needed.

        Args:
            message (UserTask): The user task to be handled.
            ctx (MessageContext): The context of the message.

        Raises:
            ValueError: If an unknown tool is called.
        """
        if not message.task_id:
            logger.error("UserTask received without a task_id.")
            # Handle error appropriately - maybe raise an exception or return
            return
        # Send the task to the LLM.
        llm_result = await self._model_client.create(
            messages=[self._system_message] + message.context,
            tools=self._tool_schema + self._delegate_tool_schema,
            cancellation_token=ctx.cancellation_token,
        )
        print(f"{'-'*80}\n{self.id.type}:\n{llm_result.content}", flush=True)

        # Process the LLM result.
        while isinstance(llm_result.content, list) and all(
            isinstance(m, FunctionCall) for m in llm_result.content
        ):
            tool_call_results: List[FunctionExecutionResult] = []
            delegate_targets: List[Tuple[str, UserTask]] = []
            # Process each function call.
            for call in llm_result.content:
                arguments = json.loads(call.arguments)
                if call.name in self._tools:
                    tool = self._tools[call.name]  # Get the Tool object
                    is_streaming_tool = isinstance(tool, StreamingFunctionTool)
                    # Check if the tool is a streaming workflow tool (you might need a better way to identify streaming tools)

                    if is_streaming_tool:
                        print(
                            f"{'-'*80}\n{self.id.type}:\nCalling streaming tool: {call.name}",
                            flush=True,
                        )
                        try:
                            stream_output_generator = await self.use_tool(  # Use a_use_tool for async generator
                                tool_name=call.name,
                                parameters=arguments,  # Assuming parameters are under 'parameters' key
                                cancellation_token=ctx.cancellation_token,
                            )
                        except Exception as tool_exec_error:
                            error_content = json.dumps(
                                {"error": f"Tool execution failed {tool_exec_error}"}
                            )
                            await self.publish_message(
                                AgentResponse(
                                    context=[
                                        AssistantMessage(
                                            content=error_content, source=self.id.type
                                        )
                                    ],
                                    reply_to_topic_type=self._agent_topic_type,
                                    task_id=message.task_id,
                                    is_final=False,  # Keep task_id for tracking
                                ),
                                topic_id=TopicId(
                                    "HeadAgent", source=self.id.key
                                ),  # Or whichever topic you want to send to
                            )
                            tool_call_results.append(
                                FunctionExecutionResult(
                                    name=call.name,
                                    call_id=call.id,
                                    content=f"Tool execution failed: {tool_exec_error}",
                                    is_error=True,
                                )
                            )
                            continue

                        if stream_output_generator:
                            print(
                                f"{'-'*80}\n{self.id.type}:\nStreaming output from tool: {call.name}",
                                flush=True,
                            )
                            async for (
                                stream_chunk_json_str
                            ) in (
                                stream_output_generator
                            ):  # Async iteration over generator
                                stream_chunk = json.loads(
                                    stream_chunk_json_str
                                )  # Parse JSON string back to dict
                                chunk_content = ""  # Prepare content for AgentResponse
                                if "stream_data" in stream_chunk:
                                    data = stream_chunk["stream_data"]
                                    print(
                                        f"{self.id.type} - Stream Data: {data}"
                                    )  # Log/process stream data
                                    chunk_content = json.dumps(
                                        {"stream_data": data}
                                    )  # Re-serialize for AgentResponse
                                elif "stream_raw_data" in stream_chunk:
                                    raw_data = stream_chunk["stream_raw_data"]
                                    print(f"{self.id.type} - Raw Data: {raw_data}")
                                    chunk_content = json.dumps(
                                        {"stream_raw_data": raw_data}
                                    )
                                elif "stream_line" in stream_chunk:
                                    line = stream_chunk["stream_line"]
                                    print(f"{self.id.type} - Stream Line: {line}")
                                    chunk_content = json.dumps({"stream_line": line})
                                elif "error" in stream_chunk:
                                    error_message = stream_chunk["error"]
                                    print(
                                        f"{self.id.type} - Stream Error: {error_message}"
                                    )
                                    chunk_content = json.dumps({"error": error_message})
                                elif "error_details" in stream_chunk:
                                    error_details = stream_chunk["error_details"]
                                    print(
                                        f"{self.id.type} - Stream Error Details: {error_details}"
                                    )
                                    chunk_content = json.dumps(
                                        {"error_details": error_details}
                                    )
                                else:
                                    print(
                                        f"{self.id.type} - Unknown Stream Chunk: {stream_chunk}"
                                    )
                                    chunk_content = json.dumps(
                                        {"unknown_chunk": stream_chunk}
                                    )

                                # Send AgentResponse for each chunk immediately
                                if chunk_content:  # Only send if there's content
                                    # Create an AgentResponse with just this chunk, not the whole context
                                    stream_response = AgentResponse(
                                        context=[
                                            AssistantMessage(
                                                content=chunk_content,
                                                source=self.id.type,
                                            )
                                        ],
                                        reply_to_topic_type=self._agent_topic_type,
                                        task_id=message.task_id,
                                        is_final=False,
                                        timestamp=datetime.now().isoformat(),  # Add timestamp for ordering
                                    )

                                    # Publish each chunk immediately
                                    await self.publish_message(
                                        stream_response,
                                        topic_id=TopicId(
                                            "HeadAgent", source=self.id.key
                                        ),
                                    )

                                    # Small delay to allow processing of the message by consumers
                                    # This prevents flooding the event loop with too many messages at once
                                    await asyncio.sleep(0.01)

                            print(
                                f"{'-'*80}\n{self.id.type}:\nEnd of stream from tool: {call.name}",
                                flush=True,
                            )
                            # No need to aggregate tool_call_results for streaming, as responses are sent chunk by chunk
                            tool_call_results.append(
                                FunctionExecutionResult(
                                    name=call.name,
                                    call_id=call.id,
                                    content="Streaming completed.",
                                    is_error=False,
                                )  # Indicate stream completion
                            )

                        else:

                            tool_call_results.append(
                                FunctionExecutionResult(
                                    name=call.name,
                                    call_id=call.id,
                                    content="Streaming tool execution failed to start.",
                                    is_error=True,
                                )
                            )

                    else:  # For non-streaming tools, use existing logic
                        result = await tool.run_json(arguments, ctx.cancellation_token)
                        result_as_str = tool.return_value_as_string(result)
                        tool_call_results.append(
                            FunctionExecutionResult(
                                name=call.name,
                                call_id=call.id,
                                content=result_as_str,
                                is_error=False,
                            )
                        )

                elif call.name in self._delegate_tools:
                    # ... (Delegate tool logic - keep as is) ...
                    result = await self._delegate_tools[call.name].run_json(
                        arguments, ctx.cancellation_token
                    )
                    topic_type = self._delegate_tools[call.name].return_value_as_string(
                        result
                    )
                    # Create the context for the delegate agent.
                    delegate_messages = list(message.context) + [
                        AssistantMessage(content=[call], source=self.id.type),
                        FunctionExecutionResultMessage(
                            content=[
                                FunctionExecutionResult(
                                    call_id=call.id,
                                    name=call.name,
                                    content=f"Transferred to {topic_type}. Adopt persona immediately.",
                                    is_error=False,
                                )
                            ]
                        ),
                    ]
                    delegate_targets.append(
                        (
                            topic_type,
                            UserTask(
                                context=delegate_messages, task_id=message.task_id
                            ),
                        )
                    )  # Pass task_id here
                else:
                    raise ValueError(f"Unknown tool: {call.name}")

            if delegate_targets:
                # Delegate the task to other agents by publishing messages to the corresponding topics.
                for topic_type, task in delegate_targets:
                    print(
                        f"{'-'*80}\n{self.id.type}:\nDelegating to {topic_type}",
                        flush=True,
                    )
                    await self.publish_message(
                        task, topic_id=TopicId(topic_type, source=self.id.key)
                    )

                return

            if tool_call_results:
                print(
                    f"{'-'*80}\n{self.id.type}:\nTool results: {tool_call_results}",
                    flush=True,
                )
                # Make another LLM call with the tool execution results.
                next_llm_context = list(message.context) + [
                    AssistantMessage(
                        content=llm_result.content, source=self.id.type
                    ),  # Previous assistant turn (tool calls)
                    FunctionExecutionResultMessage(
                        content=tool_call_results
                    ),  # Tool results
                ]
                llm_result = await self._model_client.create(
                    messages=[self._system_message] + next_llm_context,
                    tools=self._tool_schema + self._delegate_tool_schema,
                    cancellation_token=ctx.cancellation_token,
                )
                print(f"{'-'*80}\n{self.id.type}:\n{llm_result.content}", flush=True)

                message.context.extend(
                    [
                        AssistantMessage(
                            content=llm_result.content, source=self.id.type
                        ),  # Previous assistant turn (tool calls)
                        FunctionExecutionResultMessage(
                            content=tool_call_results
                        ),  # Tool results
                    ]
                )
            else:
                # If the task has been delegated, exit the loop.
                return
        final_content = llm_result.content
        if not isinstance(final_content, str):
            # Handle unexpected content type (e.g., if LLM still returns FunctionCall unexpectedly)
            final_content = json.dumps(
                {"error": "Unexpected final content type from LLM."}
            )  # Send error back

        # Append the final assistant message to the context
        message.context.append(
            AssistantMessage(content=final_content, source=self.id.type)
        )

        print(
            f"{'-'*80}\n{self.id.type}:\nPublishing FINAL response. Task ID: {message.task_id}",
            flush=True,
        )  # Log final publish
        # Task is complete; publish the final result (for non-streaming cases or final LLM response).
        message.context.append(
            AssistantMessage(content=llm_result.content, source=self.id.type)
        )
        await self.publish_message(
            AgentResponse(
                context=message.context,
                reply_to_topic_type=self._agent_topic_type,
                task_id=message.task_id,
                is_final=True,
            ),  # Pass task_id here too
            topic_id=TopicId(
                "HeadAgent", source=self.id.key
            ),  # Reply to HeadAgent Topic
        )


# --- Asynchronous factory to create an AIAgent with awaited tools ---


async def create_agent(
    description: str,
    system_message: SystemMessage,
    model_client: ChatCompletionClient,
    tool_coroutines: List[asyncio.Future],
    delegate_tools: List[Tool],
    agent_topic_type: str,
    user_topic_type: str,
) -> AIAgent:
    """
    Creates an AIAgent instance after awaiting the completion of tool creation coroutines.

    Args:
        description (str): A brief description of the agent's role.
        system_message (SystemMessage): The system message guiding the agent's behavior.
        model_client (ChatCompletionClient): The language model client for generating responses.
        tool_coroutines (List[asyncio.Future]): A list of coroutines that create tools.
        delegate_tools (List[Tool]): A list of tools for delegating tasks to other agents.
        agent_topic_type (str): The topic type for messages this agent handles.
        user_topic_type (str): The topic type for user-related messages.

    Returns:
        AIAgent: The created AIAgent instance.
    """
    tools = await asyncio.gather(*tool_coroutines)
    return AIAgent(
        description=description,
        system_message=system_message,
        model_client=model_client,
        tools=tools,
        delegate_tools=delegate_tools,
        agent_topic_type=agent_topic_type,
        user_topic_type=user_topic_type,
    )


# --- Example usage ---
# The following code is for illustrative purposes. In your application,
# the event loop will likely be managed by your framework (e.g., FastAPI).

if __name__ == "__main__":

    async def main() -> None:
        """
        Example usage of the create_agent function.
        """
        # Dummy objects for demonstration purposes
        dummy_system_message = SystemMessage(content="System prompt")
        dummy_model_client = ChatCompletionClient(api_key="dummy_key")

        # Suppose create_tool is an async function returning a Tool instance.
        async def create_tool(name: str) -> Tool:
            """
            Creates a dummy tool for demonstration purposes.

            Args:
                name (str): The name of the tool.

            Returns:
                Tool: A dummy tool instance.
            """
            # Replace with actual tool creation logic.
            dummy_tool = Tool(name=name, schema={"type": "dummy"})
            return dummy_tool

        # Create a list of tool coroutines.
        tool_coroutines = [
            create_tool("create_script_audio_image"),
            create_tool("create_blog"),
        ]
        delegate_tools = []  # Populate as needed.

        agent = await create_agent(
            description="Marketing Agent",
            system_message=dummy_system_message,
            model_client=dummy_model_client,
            tool_coroutines=tool_coroutines,
            delegate_tools=delegate_tools,
            agent_topic_type="MarketingAgent",
            user_topic_type="user_topic",
        )
        # Here you might subscribe the agent, publish tasks, etc.
        print("Agent created successfully:", agent.id)

    asyncio.run(main())
