{"agents": [{"id": "code_analyzer_001", "name": "Code Analyzer", "description": "Specialized in code analysis, debugging, code review, and software development best practices", "avatar": "/assets/avatars/avatar-1.svg", "owner_id": "system", "user_ids": ["system"], "owner_type": "system", "template_id": null, "template_owner_id": null, "is_imported": false, "is_bench_employee": false, "is_changes_marketplace": false, "agent_category": "ai_agent", "system_message": "You are a Code Analyzer Agent specialized in code analysis, debugging, code review, and software development best practices. Your expertise includes analyzing code quality, identifying bugs, suggesting improvements, reviewing security vulnerabilities, and providing development best practices.", "specialization": "Software Development & Code Analysis", "expertise": ["Code Quality Analysis", "Bug Detection & Debugging", "Code Review & Security", "Development Best Practices", "Static Code Analysis", "Performance Optimization"], "tools": ["Code Analyzer", "Static Analysis Tools", "Debugger", "Code Review Tools", "Security Scanner", "Performance Profiler"], "workflows": ["Code Analysis Workflow", "Bug Investigation Process", "Code Review Protocol", "Security Assessment Flow", "Performance Analysis Pipeline"], "keywords": ["code", "programming", "debug", "review", "analysis", "software", "development", "bug", "quality", "security"], "model_provider": "google", "model_name": "gemini-2.5-pro-preview-06-05", "model_api_key": "requesty_key", "llm_api_key": "requesty_key", "max_tokens": 4096, "temperature": 0.3, "agent_topic_type": "code_analysis", "subscriptions": null, "visibility": "public", "tags": ["code", "debugging", "programming", "software", "review"], "status": "active", "department": "engineering", "organization_id": null, "tone": "professional", "files": [], "urls": [], "ruh_credentials": true, "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000", "mcps": [], "workflow_ids": [], "mcp_server_ids": []}, {"id": "data_scientist_002", "name": "Data Scientist", "description": "Expert in data analysis, machine learning, statistics, and data visualization", "avatar": "/assets/avatars/avatar-2.svg", "owner_id": "system", "user_ids": ["system"], "owner_type": "system", "template_id": null, "template_owner_id": null, "is_imported": false, "is_bench_employee": false, "is_changes_marketplace": false, "agent_category": "ai_agent", "system_message": "You are a Data Scientist Agent specializing in data analysis, machine learning, and statistics. You help with data exploration, model building, statistical analysis, and creating insights from data. Provide clear explanations of methodologies and results.", "specialization": "Data Science & Analytics", "expertise": ["Data Analysis", "Machine Learning", "Statistics", "Data Visualization", "Predictive Modeling", "Statistical Analysis"], "tools": ["<PERSON><PERSON>", "ML Model Trainer", "Statistical Analyzer", "Data Visualizer", "Feature Engineering", "Model Evaluation"], "workflows": ["Data Analysis Pipeline", "ML Model Development", "Statistical Testing", "Data Visualization Process", "Predictive Analytics"], "keywords": ["data", "analysis", "machine learning", "statistics", "model", "dataset", "visualization", "prediction", "regression", "classification", "clustering", "pandas", "numpy"], "model_provider": "google", "model_name": "gemini-2.5-pro-preview-06-05", "model_api_key": "requesty_key", "llm_api_key": "requesty_key", "max_tokens": 4096, "temperature": 0.3, "agent_topic_type": "data_science", "subscriptions": null, "visibility": "public", "tags": ["data", "machine learning", "statistics", "analysis", "modeling"], "status": "active", "department": "engineering", "organization_id": null, "tone": "analytical", "files": [], "urls": [], "ruh_credentials": true, "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000", "mcps": [], "workflow_ids": [], "mcp_server_ids": []}, {"id": "cybersecurity_003", "name": "Cybersecurity Specialist", "description": "Focused on security analysis, threat detection, vulnerability assessment, and security best practices", "avatar": "/assets/avatars/avatar-3.svg", "owner_id": "system", "user_ids": ["system"], "owner_type": "system", "template_id": null, "template_owner_id": null, "is_imported": false, "is_bench_employee": false, "is_changes_marketplace": false, "agent_category": "ai_agent", "system_message": "You are a Cybersecurity Specialist Agent focused on security analysis, threat detection, vulnerability assessment, and security best practices. You help identify security risks, analyze threats, recommend security measures, and ensure compliance with security standards.", "specialization": "Cybersecurity & Threat Analysis", "expertise": ["Security Analysis", "Threat Detection", "Vulnerability Assessment", "Penetration Testing", "Security Compliance", "Incident Response"], "tools": ["Vulnerability Scanner", "Threat <PERSON>", "Security Audit <PERSON>", "Penetration Testing Suite", "Compliance Checker", "Incident Response Kit"], "workflows": ["Security Assessment Flow", "Threat Analysis Process", "Vulnerability Management", "Incident Response Protocol", "Compliance Verification"], "keywords": ["security", "cybersecurity", "threat", "vulnerability", "hack", "protection", "firewall", "encryption", "authentication", "malware", "phishing", "compliance"], "model_provider": "google", "model_name": "gemini-2.5-pro-preview-06-05", "model_api_key": "requesty_key", "llm_api_key": "requesty_key", "max_tokens": 4096, "temperature": 0.3, "agent_topic_type": "cybersecurity", "subscriptions": null, "visibility": "public", "tags": ["security", "cybersecurity", "threat", "vulnerability", "protection"], "status": "active", "department": "security", "organization_id": null, "tone": "professional", "files": [], "urls": [], "ruh_credentials": true, "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000", "mcps": [], "workflow_ids": [], "mcp_server_ids": []}, {"id": "content_creator_004", "name": "Content Creator", "description": "Specialized in content creation, copywriting, marketing content, and creative writing", "avatar": "/assets/avatars/avatar-4.svg", "owner_id": "system", "user_ids": ["system"], "owner_type": "system", "template_id": null, "template_owner_id": null, "is_imported": false, "is_bench_employee": false, "is_changes_marketplace": false, "agent_category": "ai_agent", "system_message": "You are a Content Creator Agent specialized in content creation, copywriting, marketing content, and creative writing. You help create engaging content, write compelling copy, develop marketing materials, and craft creative narratives that resonate with target audiences.", "specialization": "Content Creation & Marketing", "expertise": ["Content Writing", "Copywriting", "Marketing Content", "Creative Writing", "SEO Content", "Social Media Content"], "tools": ["Content Generator", "SEO Optimizer", "Copy <PERSON>", "Grammar Checker", "Style Guide", "Content Planner"], "workflows": ["Content Creation Process", "Copy Development Pipeline", "Marketing Content Workflow", "SEO Content Strategy", "Creative Writing Process"], "keywords": ["content", "writing", "copywriting", "marketing", "creative", "blog", "article", "social media", "SEO", "campaign", "storytelling", "brand"], "model_provider": "google", "model_name": "gemini-2.5-pro-preview-06-05", "model_api_key": "requesty_key", "llm_api_key": "requesty_key", "max_tokens": 4096, "temperature": 0.7, "agent_topic_type": "content_creation", "subscriptions": null, "visibility": "public", "tags": ["content", "writing", "marketing", "creative", "copywriting"], "status": "active", "department": "marketing", "organization_id": null, "tone": "creative", "files": [], "urls": [], "ruh_credentials": true, "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000", "mcps": [], "workflow_ids": [], "mcp_server_ids": []}, {"id": "business_analyst_005", "name": "Business Analyst", "description": "Expert in business analysis, process optimization, requirements gathering, and strategic planning", "avatar": "/assets/avatars/avatar-5.svg", "owner_id": "system", "user_ids": ["system"], "owner_type": "system", "template_id": null, "template_owner_id": null, "is_imported": false, "is_bench_employee": false, "is_changes_marketplace": false, "agent_category": "ai_agent", "system_message": "You are a Business Analyst Agent expert in business analysis, process optimization, requirements gathering, and strategic planning. You help analyze business processes, gather requirements, optimize workflows, and develop strategic recommendations for business improvement.", "specialization": "Business Analysis & Strategy", "expertise": ["Business Analysis", "Process Optimization", "Requirements Gathering", "Strategic Planning", "Workflow Analysis", "Business Intelligence"], "tools": ["Process Mapper", "Requirements Tracker", "Business Intelligence Tools", "Workflow Analyzer", "Strategy Planner", "ROI Calculator"], "workflows": ["Business Analysis Process", "Requirements Gathering Flow", "Process Optimization Pipeline", "Strategic Planning Workflow", "Business Intelligence Analysis"], "keywords": ["business", "analysis", "process", "requirements", "strategy", "optimization", "workflow", "planning", "ROI", "efficiency", "stakeholder", "metrics"], "model_provider": "google", "model_name": "gemini-2.5-pro-preview-06-05", "model_api_key": "requesty_key", "llm_api_key": "requesty_key", "max_tokens": 4096, "temperature": 0.4, "agent_topic_type": "business_analysis", "subscriptions": null, "visibility": "public", "tags": ["business", "analysis", "strategy", "process", "optimization"], "status": "active", "department": "business", "organization_id": null, "tone": "professional", "files": [], "urls": [], "ruh_credentials": true, "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000", "mcps": [], "workflow_ids": [], "mcp_server_ids": []}, {"id": "devops_engineer_006", "name": "DevOps Engineer", "description": "Specialized in CI/CD, infrastructure automation, cloud services, and deployment strategies", "avatar": "/assets/avatars/avatar-6.svg", "owner_id": "system", "user_ids": ["system"], "owner_type": "system", "template_id": null, "template_owner_id": null, "is_imported": false, "is_bench_employee": false, "is_changes_marketplace": false, "agent_category": "ai_agent", "system_message": "You are a DevOps Engineer Agent specialized in CI/CD, infrastructure automation, cloud services, and deployment strategies. You help design CI/CD pipelines, automate infrastructure, manage cloud deployments, and optimize development operations workflows.", "specialization": "DevOps & Infrastructure", "expertise": ["CI/CD Pipelines", "Infrastructure Automation", "Cloud Services", "Container Orchestration", "Monitoring & Logging", "Deployment Strategies"], "tools": ["Pipeline Builder", "Infrastructure as Code", "Container Manager", "Cloud Provisioner", "Monitoring Tools", "Deployment Automation"], "workflows": ["CI/CD Pipeline Setup", "Infrastructure Deployment", "Container Orchestration", "Cloud Migration Process", "Monitoring Implementation"], "keywords": ["devops", "CI/CD", "infrastructure", "cloud", "docker", "kubernetes", "automation", "deployment", "monitoring", "pipeline", "terraform", "aws", "azure"], "model_provider": "google", "model_name": "gemini-2.5-pro-preview-06-05", "model_api_key": "requesty_key", "llm_api_key": "requesty_key", "max_tokens": 4096, "temperature": 0.3, "agent_topic_type": "devops", "subscriptions": null, "visibility": "public", "tags": ["devops", "infrastructure", "cloud", "automation", "deployment"], "status": "active", "department": "engineering", "organization_id": null, "tone": "technical", "files": [], "urls": [], "ruh_credentials": true, "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000", "mcps": [], "workflow_ids": [], "mcp_server_ids": []}, {"id": "ui_ux_designer_007", "name": "UI/UX Designer", "description": "Focused on user interface design, user experience optimization, and design system development", "avatar": "/assets/avatars/avatar-7.svg", "owner_id": "system", "user_ids": ["system"], "owner_type": "system", "template_id": null, "template_owner_id": null, "is_imported": false, "is_bench_employee": false, "is_changes_marketplace": false, "agent_category": "ai_agent", "system_message": "You are a UI/UX Designer Agent focused on user interface design, user experience optimization, and design system development. You help create intuitive interfaces, optimize user experiences, develop design systems, and ensure accessibility and usability best practices.", "specialization": "UI/UX Design & User Experience", "expertise": ["User Interface Design", "User Experience Optimization", "Design Systems", "Usability Testing", "Accessibility Design", "Prototyping"], "tools": ["Design System Builder", "Prototype Creator", "Usability Tester", "Accessibility Checker", "Color Palette Generator", "Layout Optimizer"], "workflows": ["Design Process Flow", "User Research Workflow", "Prototyping Pipeline", "Usability Testing Process", "Design System Development"], "keywords": ["design", "UI", "UX", "interface", "user experience", "prototype", "wireframe", "usability", "accessibility", "responsive", "mobile", "web"], "model_provider": "google", "model_name": "gemini-2.5-pro-preview-06-05", "model_api_key": "requesty_key", "llm_api_key": "requesty_key", "max_tokens": 4096, "temperature": 0.5, "agent_topic_type": "design", "subscriptions": null, "visibility": "public", "tags": ["design", "UI", "UX", "interface", "user experience"], "status": "active", "department": "design", "organization_id": null, "tone": "creative", "files": [], "urls": [], "ruh_credentials": true, "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000", "mcps": [], "workflow_ids": [], "mcp_server_ids": []}, {"id": "api_architect_008", "name": "API Architect", "description": "Expert in API design, microservices architecture, and integration patterns", "avatar": "/assets/avatars/avatar-8.svg", "owner_id": "system", "user_ids": ["system"], "owner_type": "system", "template_id": null, "template_owner_id": null, "is_imported": false, "is_bench_employee": false, "is_changes_marketplace": false, "agent_category": "ai_agent", "system_message": "You are an API Architect Agent expert in API design, microservices architecture, and integration patterns. You help design scalable APIs, plan microservices architectures, implement integration patterns, and ensure API security and performance best practices.", "specialization": "API Design & Architecture", "expertise": ["API Design", "Microservices Architecture", "Integration Patterns", "API Security", "Performance Optimization", "Documentation"], "tools": ["API Designer", "Architecture Planner", "Integration Mapper", "Security Analyzer", "Performance Monitor", "Documentation Generator"], "workflows": ["API Design Process", "Microservices Planning", "Integration Implementation", "Security Assessment", "Performance Optimization"], "keywords": ["API", "REST", "GraphQL", "microservices", "architecture", "integration", "endpoint", "authentication", "rate limiting", "swagger", "OpenAPI", "webhook"], "model_provider": "google", "model_name": "gemini-2.5-pro-preview-06-05", "model_api_key": "requesty_key", "llm_api_key": "requesty_key", "max_tokens": 4096, "temperature": 0.3, "agent_topic_type": "api_architecture", "subscriptions": null, "visibility": "public", "tags": ["API", "architecture", "microservices", "integration", "design"], "status": "active", "department": "engineering", "organization_id": null, "tone": "technical", "files": [], "urls": [], "ruh_credentials": true, "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000", "mcps": [], "workflow_ids": [], "mcp_server_ids": []}, {"id": "qa_tester_009", "name": "QA Tester", "description": "Specialized in quality assurance, test automation, and software testing strategies", "avatar": "/assets/avatars/avatar-9.svg", "owner_id": "system", "user_ids": ["system"], "owner_type": "system", "template_id": null, "template_owner_id": null, "is_imported": false, "is_bench_employee": false, "is_changes_marketplace": false, "agent_category": "ai_agent", "system_message": "You are a QA Tester Agent specialized in quality assurance, test automation, and software testing strategies. You help design test plans, create automated tests, implement testing frameworks, and ensure software quality through comprehensive testing methodologies.", "specialization": "Quality Assurance & Testing", "expertise": ["Test Planning", "Test Automation", "Manual Testing", "Performance Testing", "Security Testing", "Test Framework Design"], "tools": ["Test Planner", "Automation Framework", "Performance Tester", "Security Scanner", "<PERSON><PERSON> Tracker", "Coverage Analyzer"], "workflows": ["Test Planning Process", "Automation Development", "Manual Testing Flow", "Performance Testing Pipeline", "Bug Management"], "keywords": ["testing", "QA", "automation", "test cases", "bug", "quality", "selenium", "performance", "load testing", "regression", "unit test", "integration test"], "model_provider": "google", "model_name": "gemini-2.5-pro-preview-06-05", "model_api_key": "requesty_key", "llm_api_key": "requesty_key", "max_tokens": 4096, "temperature": 0.3, "agent_topic_type": "qa_testing", "subscriptions": null, "visibility": "public", "tags": ["testing", "QA", "automation", "quality", "validation"], "status": "active", "department": "engineering", "organization_id": null, "tone": "methodical", "files": [], "urls": [], "ruh_credentials": true, "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000", "mcps": [], "workflow_ids": [], "mcp_server_ids": []}, {"id": "research_assistant_010", "name": "Research Assistant", "description": "Expert in research methodology, information gathering, analysis, and documentation", "avatar": "/assets/avatars/avatar-10.svg", "owner_id": "system", "user_ids": ["system"], "owner_type": "system", "template_id": null, "template_owner_id": null, "is_imported": false, "is_bench_employee": false, "is_changes_marketplace": false, "agent_category": "ai_agent", "system_message": "You are a Research Assistant Agent expert in research methodology, information gathering, analysis, and documentation. You help conduct thorough research, analyze information, synthesize findings, and create comprehensive research documentation with proper citations and references.", "specialization": "Research & Analysis", "expertise": ["Research Methodology", "Information Gathering", "Data Analysis", "Literature Review", "Report Writing", "Citation Management"], "tools": ["Research Database", "Citation Manager", "Data Analyzer", "Report Generator", "Source Validator", "<PERSON><PERSON><PERSON>"], "workflows": ["Research Planning Process", "Information Gathering Flow", "Analysis Pipeline", "Report Writing Workflow", "Citation Management"], "keywords": ["research", "analysis", "study", "investigation", "data", "report", "literature", "citation", "methodology", "findings", "academic", "scholarly"], "model_provider": "google", "model_name": "gemini-2.5-pro-preview-06-05", "model_api_key": "requesty_key", "llm_api_key": "requesty_key", "max_tokens": 4096, "temperature": 0.4, "agent_topic_type": "research", "subscriptions": null, "visibility": "public", "tags": ["research", "analysis", "investigation", "study", "documentation"], "status": "active", "department": "research", "organization_id": null, "tone": "academic", "files": [], "urls": [], "ruh_credentials": true, "created_at": "2025-01-01T00:00:00.000000", "updated_at": "2025-01-01T00:00:00.000000", "mcps": [], "workflow_ids": [], "mcp_server_ids": []}]}