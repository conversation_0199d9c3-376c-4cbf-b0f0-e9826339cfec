import asyncio
import json
import logging
from pprint import pprint
from ..tools.workflow_tool_loader import WorkflowToolLoader
from ..helper.api_call import AuthType
from ..shared.config.logging_config import setup_logging

# Configure logging
logger = logging.getLogger(__name__)

async def main():
    """
    Example of loading and using workflow tools.
    """
    # Set up logging
    setup_logging()
    logger.info("Starting workflow tool example")
    
    # Initialize the workflow tool loader
    loader = WorkflowToolLoader()
    logger.info("Initialized workflow tool loader")
    
    # Example 1: Create a tool from a single workflow metadata
    workflow_metadata = {
        "id": "workflow-123",
        "name": "Data Processing Workflow",
        "description": "Process data and generate insights",
        "workflow_url": "https://api.example.com/workflows/workflow-123",
        "builder_url": "https://builder.example.com/workflows/workflow-123",
        "start_nodes": ["node-1"],
        "owner_id": "user-1",
        "user_ids": ["user-1", "user-2"],
        "owner_type": "user",
        "workflow_template_id": "template-1",
        "parent_workflow_id": None,
        "url": "https://api.example.com/workflows/workflow-123",
        "is_imported": False,
        "execution_count": 10,
        "version": "1.0.0",
        "visibility": "private",
        "category": "data-processing",
        "tags": {"type": "etl"},
        "status": "active",
        "created_at": "2023-01-01T00:00:00Z",
        "updated_at": "2023-01-02T00:00:00Z",
        "parameters": {
            "type": "object",
            "properties": {
                "input_data": {
                    "type": "object",
                    "description": "Input data for the workflow"
                },
                "parameters": {
                    "type": "object",
                    "description": "Optional parameters for workflow execution"
                }
            },
            "required": ["input_data"]
        }
    }
    
    logger.info(f"Creating tool from workflow metadata: {workflow_metadata['id']}")
    workflow_tool = loader.create_workflow_tool_from_metadata(workflow_metadata)
    logger.info(f"Created tool: {workflow_tool.name} - {workflow_tool.description}")
    
    # Example 2: Load all workflows as tools
    # This would make an API call to get all workflows
    # For demonstration, we'll just print what would happen
    logger.info("Would load all workflows as tools from API")
    
    # Example 3: Execute a workflow tool
    # This would actually execute the workflow
    # For demonstration, we'll just print what would happen
    logger.info("Would execute workflow with parameters")
    example_args = {
        "input_data": {
            "source_file": "data.csv",
            "format": "csv"
        },
        "parameters": {
            "max_rows": 1000,
            "include_headers": True
        }
    }
    logger.debug(f"Example args: {example_args}")
    
    logger.info("Example completed")

if __name__ == "__main__":
    asyncio.run(main())
