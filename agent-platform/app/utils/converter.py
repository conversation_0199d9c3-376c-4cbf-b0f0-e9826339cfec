from slugify import slugify
import keyword
import re


def to_valid_identifier(name):
    # Generate a slug with underscores
    slug = slugify(name, separator="_")

    # Remove any characters that are not letters, digits, or underscores
    slug = re.sub(r"\W|^(?=\d)", "_", slug)

    # Ensure the slug doesn't start with a digit
    if slug and slug[0].isdigit():
        slug = "_" + slug

    # Ensure the slug is not a Python keyword
    if keyword.iskeyword(slug):
        slug += "_agent"

    return slug
