import json
import uuid
from datetime import datetime
from typing import Dict, Optional

from redis import Redis
from pydantic import BaseModel

class SessionConfig(BaseModel):
    agent_id: str
    agent_config: Dict
    created_at: str

class SessionManager:
    def __init__(self, redis_client: Redis, ttl_seconds: int = 3600):
        self.redis = redis_client
        self.ttl = ttl_seconds

    def create_session(self, agent_id: str, agent_config: Dict) -> str:
        session_id = str(uuid.uuid4())
        session_data = SessionConfig(
            agent_id=agent_id,
            agent_config=agent_config,
            created_at=datetime.utcnow().isoformat()
        )
        
        key = f"session:{session_id}"
        self.redis.hset(
            key,
            mapping={
                "agent_id": session_data.agent_id,
                "agent_config": json.dumps(session_data.agent_config),
                "created_at": session_data.created_at
            }
        )
        self.redis.expire(key, self.ttl)
        return session_id

    def get_session(self, session_id: str) -> Optional[SessionConfig]:
        key = f"session:{session_id}"
        data = self.redis.hgetall(key)
        
        if not data:
            return None
            
        return SessionConfig(
            agent_id=data[b"agent_id"].decode(),
            agent_config=json.loads(data[b"agent_config"].decode()),
            created_at=data[b"created_at"].decode()
        )