import pytest
from app.shared.config.base import get_settings

def test_settings_loading():
    settings = get_settings()
    assert settings is not None
    assert hasattr(settings, "api_key")
    assert hasattr(settings, "base_url")

def test_environment_override(monkeypatch):
    monkeypatch.setenv("API_KEY", "test_key")
    settings = get_settings()
    assert settings.api_key == "test_key"

def test_invalid_config():
    with pytest.raises(ValueError):
        # Test with invalid configuration
        get_settings("nonexistent_env")