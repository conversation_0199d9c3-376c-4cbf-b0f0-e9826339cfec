#!/usr/bin/env python3
"""
Test script for Pinecone memory integration with agents.

This script demonstrates and tests:
1. Pinecone memory initialization
2. Agent knowledge storage and retrieval
3. Conversation memory persistence
4. Memory-enhanced agent interactions
"""

import asyncio
import logging
import os
import sys
from typing import Dict, Any, List

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from app.memory import PineconeMemory, memory_manager
from app.autogen_service.agent_factory import AgentFactory
from app.schemas.api import AgentConfig
from autogen_core.model_context import BufferedChatCompletionContext
from autogen_core.memory import MemoryContent, MemoryMimeType

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class PineconeMemoryTester:
    """Test class for Pinecone memory integration."""
    
    def __init__(self):
        self.logger = logger
        self.agent_factory = AgentFactory()
        self.test_agent_id = "test-agent-123"
        self.test_user_id = "test-user-456"

    async def test_pinecone_memory_basic_operations(self) -> Dict[str, Any]:
        """Test basic Pinecone memory operations."""
        results = {
            "memory_creation": False,
            "knowledge_addition": False,
            "knowledge_retrieval": False,
            "conversation_memory": False,
            "memory_stats": False,
        }
        
        try:
            self.logger.info("Testing basic Pinecone memory operations...")
            
            # Test 1: Create Pinecone memory
            memory = memory_manager.get_agent_memory(
                agent_id=self.test_agent_id,
                user_id=self.test_user_id,
                namespace=f"test-{self.test_agent_id}",
            )
            results["memory_creation"] = True
            self.logger.info("✅ Pinecone memory created successfully")
            
            # Test 2: Add knowledge to memory
            knowledge_items = [
                {
                    "content": "Python is a high-level programming language known for its simplicity and readability.",
                    "metadata": {"topic": "programming", "language": "python"},
                    "source": "knowledge_base"
                },
                {
                    "content": "Machine learning is a subset of artificial intelligence that enables computers to learn without being explicitly programmed.",
                    "metadata": {"topic": "ai", "subtopic": "machine_learning"},
                    "source": "knowledge_base"
                },
                {
                    "content": "FastAPI is a modern, fast web framework for building APIs with Python based on standard Python type hints.",
                    "metadata": {"topic": "programming", "framework": "fastapi"},
                    "source": "knowledge_base"
                }
            ]
            
            added_count = await memory_manager.add_agent_knowledge(
                agent_id=self.test_agent_id,
                knowledge_items=knowledge_items,
                user_id=self.test_user_id,
                knowledge_type="technical_knowledge"
            )
            
            if added_count == len(knowledge_items):
                results["knowledge_addition"] = True
                self.logger.info(f"✅ Added {added_count} knowledge items successfully")
            else:
                self.logger.warning(f"⚠️ Expected {len(knowledge_items)} items, added {added_count}")
            
            # Wait for indexing
            await asyncio.sleep(2)
            
            # Test 3: Query knowledge
            query_results = await memory_manager.query_agent_memory(
                agent_id=self.test_agent_id,
                query="What is Python programming language?",
                user_id=self.test_user_id,
                k=3
            )
            
            if query_results and len(query_results) > 0:
                results["knowledge_retrieval"] = True
                self.logger.info(f"✅ Retrieved {len(query_results)} relevant memories")
                for i, result in enumerate(query_results, 1):
                    score = result.metadata.get("score", 0)
                    self.logger.info(f"  {i}. Score: {score:.3f} - {result.content[:100]}...")
            else:
                self.logger.warning("⚠️ No knowledge retrieved from query")
            
            # Test 4: Add conversation memory
            conversation_success = await memory_manager.add_conversation_memory(
                agent_id=self.test_agent_id,
                user_message="Can you explain what Python is?",
                agent_response="Python is a high-level programming language that emphasizes code readability and simplicity. It's widely used for web development, data science, AI, and automation.",
                user_id=self.test_user_id,
                conversation_id="test-conv-123",
                metadata={"session_id": "test-session-789"}
            )
            
            if conversation_success:
                results["conversation_memory"] = True
                self.logger.info("✅ Conversation memory added successfully")
            
            # Test 5: Get memory statistics
            stats = await memory_manager.get_agent_memory_stats(
                agent_id=self.test_agent_id,
                user_id=self.test_user_id
            )
            
            if stats and "namespace_vectors" in stats:
                results["memory_stats"] = True
                self.logger.info(f"✅ Memory stats retrieved: {stats}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in basic memory operations test: {e}")
            return results

    async def test_agent_with_pinecone_memory(self) -> Dict[str, Any]:
        """Test agent creation and interaction with Pinecone memory."""
        results = {
            "agent_creation": False,
            "memory_integration": False,
            "knowledge_preload": False,
        }
        
        try:
            self.logger.info("Testing agent with Pinecone memory...")
            
            # Create agent configuration
            agent_config = AgentConfig(
                id=self.test_agent_id,
                name="TestAgent",
                description="A test agent with Pinecone memory",
                system_message="You are a helpful AI assistant with access to persistent memory. Use your knowledge to provide accurate and helpful responses.",
                model_provider="openai",
                model_name="gpt-4o-mini",
                workflows=[],
                mcps=[],
            )
            
            # Create model context
            model_context = BufferedChatCompletionContext(buffer_size=1000)
            
            # Create agent with Pinecone memory
            agent = await self.agent_factory.create_agent(
                run_id="test-run-123",
                agent_config=agent_config,
                model_context=model_context,
                memory=None,
                organization_id="test-org-789",
                use_knowledge=True,
                variables={"agent_role": "technical_assistant"},
                user_id=self.test_user_id,
                use_pinecone_memory=True,
            )
            
            if agent:
                results["agent_creation"] = True
                self.logger.info("✅ Agent created with Pinecone memory successfully")
                
                # Check if agent has memory
                if agent.memory and len(agent.memory) > 0:
                    results["memory_integration"] = True
                    self.logger.info(f"✅ Agent has {len(agent.memory)} memory instances")
                    
                    # Check if it's Pinecone memory
                    pinecone_memory = None
                    for mem in agent.memory:
                        if isinstance(mem, PineconeMemory):
                            pinecone_memory = mem
                            break
                    
                    if pinecone_memory:
                        self.logger.info("✅ Agent is using Pinecone memory")
                        
                        # Pre-load some technical knowledge
                        technical_knowledge = [
                            {
                                "content": "AutoGen is a framework for building multi-agent conversational AI systems.",
                                "metadata": {"topic": "autogen", "category": "framework"},
                                "source": "documentation"
                            },
                            {
                                "content": "Pinecone is a vector database service that enables similarity search and machine learning applications.",
                                "metadata": {"topic": "pinecone", "category": "database"},
                                "source": "documentation"
                            }
                        ]
                        
                        preload_count = await memory_manager.add_agent_knowledge(
                            agent_id=self.test_agent_id,
                            knowledge_items=technical_knowledge,
                            user_id=self.test_user_id,
                            knowledge_type="framework_knowledge"
                        )
                        
                        if preload_count == len(technical_knowledge):
                            results["knowledge_preload"] = True
                            self.logger.info(f"✅ Pre-loaded {preload_count} technical knowledge items")
                
            return results
            
        except Exception as e:
            self.logger.error(f"Error in agent memory integration test: {e}")
            return results

    async def test_memory_persistence_and_retrieval(self) -> Dict[str, Any]:
        """Test memory persistence across sessions."""
        results = {
            "cross_session_retrieval": False,
            "memory_transfer": False,
            "memory_cleanup": False,
        }
        
        try:
            self.logger.info("Testing memory persistence and retrieval...")
            
            # Test cross-session memory retrieval
            query_results = await memory_manager.query_agent_memory(
                agent_id=self.test_agent_id,
                query="Tell me about AutoGen framework",
                user_id=self.test_user_id,
                k=2
            )
            
            if query_results:
                results["cross_session_retrieval"] = True
                self.logger.info(f"✅ Retrieved {len(query_results)} memories from previous session")
            
            # Test memory transfer between agents
            target_agent_id = "target-agent-456"
            transferred_count = await memory_manager.transfer_knowledge_between_agents(
                source_agent_id=self.test_agent_id,
                target_agent_id=target_agent_id,
                query="programming language",
                user_id=self.test_user_id,
                k=2
            )
            
            if transferred_count > 0:
                results["memory_transfer"] = True
                self.logger.info(f"✅ Transferred {transferred_count} knowledge items between agents")
            
            # Test memory cleanup
            cleanup_success = await memory_manager.clear_agent_memory(
                agent_id=target_agent_id,
                user_id=self.test_user_id
            )
            
            if cleanup_success:
                results["memory_cleanup"] = True
                self.logger.info("✅ Memory cleanup successful")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error in memory persistence test: {e}")
            return results

    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive Pinecone memory integration test."""
        overall_results = {
            "basic_operations": {},
            "agent_integration": {},
            "persistence_retrieval": {},
            "overall_success": False,
        }
        
        try:
            self.logger.info("=" * 80)
            self.logger.info("STARTING COMPREHENSIVE PINECONE MEMORY INTEGRATION TEST")
            self.logger.info("=" * 80)
            
            # Test 1: Basic operations
            self.logger.info("\n1. Testing basic Pinecone memory operations...")
            overall_results["basic_operations"] = await self.test_pinecone_memory_basic_operations()
            
            # Test 2: Agent integration
            self.logger.info("\n2. Testing agent integration with Pinecone memory...")
            overall_results["agent_integration"] = await self.test_agent_with_pinecone_memory()
            
            # Test 3: Persistence and retrieval
            self.logger.info("\n3. Testing memory persistence and retrieval...")
            overall_results["persistence_retrieval"] = await self.test_memory_persistence_and_retrieval()
            
            # Calculate overall success
            all_tests_passed = True
            for test_category, test_results in overall_results.items():
                if test_category == "overall_success":
                    continue
                if isinstance(test_results, dict):
                    for test_name, result in test_results.items():
                        if not result:
                            all_tests_passed = False
                            break
                if not all_tests_passed:
                    break
            
            overall_results["overall_success"] = all_tests_passed
            
            # Summary
            self.logger.info("\n" + "=" * 80)
            self.logger.info("TEST RESULTS SUMMARY")
            self.logger.info("=" * 80)
            
            for test_category, test_results in overall_results.items():
                if test_category == "overall_success":
                    continue
                self.logger.info(f"\n{test_category.replace('_', ' ').title()}:")
                if isinstance(test_results, dict):
                    for test_name, result in test_results.items():
                        status = "✅ PASS" if result else "❌ FAIL"
                        self.logger.info(f"  {test_name.replace('_', ' ').title()}: {status}")
            
            overall_status = "✅ PASS" if overall_results["overall_success"] else "❌ FAIL"
            self.logger.info(f"\nOverall Test Result: {overall_status}")
            
            return overall_results
            
        except Exception as e:
            self.logger.error(f"Error running comprehensive test: {e}")
            overall_results["overall_success"] = False
            return overall_results

    async def cleanup_test_data(self):
        """Clean up test data from Pinecone."""
        try:
            self.logger.info("Cleaning up test data...")
            
            # Clear test agent memory
            await memory_manager.clear_agent_memory(
                agent_id=self.test_agent_id,
                user_id=self.test_user_id
            )
            
            # Close all memory instances
            await memory_manager.close_all_memories()
            
            self.logger.info("Test data cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up test data: {e}")


async def main():
    """Main test function."""
    tester = PineconeMemoryTester()
    
    try:
        # Run comprehensive test
        results = await tester.run_comprehensive_test()
        
        # Print final results
        print("\n" + "=" * 80)
        print("FINAL TEST RESULTS")
        print("=" * 80)
        print(f"Results: {results}")
        
        return results["overall_success"]
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        return False
    finally:
        # Cleanup
        await tester.cleanup_test_data()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
