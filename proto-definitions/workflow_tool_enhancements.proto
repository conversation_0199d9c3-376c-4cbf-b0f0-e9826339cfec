// Optional enhancements to workflow.proto for better tool integration support
// These are OPTIONAL and the current implementation works without them

syntax = "proto3";

package workflow;

// Enhanced component message with tool-specific metadata
message ComponentToolMetadata {
  bool can_be_tool = 1;                    // Whether component can be used as a tool
  repeated string supported_agents = 2;     // Agent types that can use this tool
  string tool_category = 3;                // Tool categorization
  int32 max_concurrent_executions = 4;     // Performance limits
  bool requires_approval = 5;              // Whether tool usage requires approval
  string tool_description = 6;             // Tool-specific description
  repeated string tool_tags = 7;           // Tool-specific tags
}

// Enhanced component schema for tool generation
message ComponentToolSchema {
  string name = 1;                         // Tool function name
  string description = 2;                  // Tool description
  string parameters_json = 3;              // JSON schema for parameters
  bool strict_mode = 4;                    // Whether to use strict parameter validation
  string return_type = 5;                  // Expected return type
}

// Enhanced component message (extends existing Component)
message EnhancedComponent {
  // All existing Component fields would be here
  string id = 1;
  string name = 2;
  string display_name = 3;
  string description = 4;
  string category = 5;
  string icon = 6;
  string type = 7;
  repeated ComponentInput inputs = 8;
  repeated ComponentOutput outputs = 9;
  bool is_valid = 10;
  bool beta = 11;
  bool requires_approval = 12;
  string path = 13;
  MCPInfo mcp_info = 14;
  
  // New tool-specific fields
  optional ComponentToolMetadata tool_metadata = 15;
  optional ComponentToolSchema tool_schema = 16;
  string component_version = 17;           // Component version for compatibility
  repeated string dependencies = 18;       // Component dependencies
}

// Tool connection information
message ToolConnection {
  string source_component_id = 1;         // Component being used as tool
  string target_agent_id = 2;             // Agent using the tool
  string connection_id = 3;                // Unique connection identifier
  string handle_id = 4;                    // Tool handle identifier (tool_1, tool_2, etc.)
  bool is_active = 5;                      // Whether connection is active
  string created_at = 6;                   // Connection creation timestamp
  map<string, string> connection_config = 7; // Connection-specific configuration
}

// Workflow with tool connections
message WorkflowWithTools {
  // All existing Workflow fields
  string id = 1;
  string name = 2;
  string description = 3;
  // ... other workflow fields
  
  // New tool-specific fields
  repeated ToolConnection tool_connections = 21;
  int32 total_tool_connections = 22;
  bool has_agentic_nodes = 23;
}

// Agent configuration with tool schemas
message AgentToolConfiguration {
  string agent_id = 1;
  string agent_name = 2;
  repeated AgentTool tools = 3;
  string configuration_version = 4;
  string created_at = 5;
  string updated_at = 6;
}

// Individual agent tool
message AgentTool {
  string tool_id = 1;
  string tool_type = 2;                    // "workflow_component" or "mcp_marketplace"
  ComponentToolInfo component_info = 3;
  string tool_schema_json = 4;             // JSON schema for the tool
  bool is_enabled = 5;
  map<string, string> tool_config = 6;     // Tool-specific configuration
}

// Component information for tools
message ComponentToolInfo {
  string component_id = 1;
  string component_type = 2;
  string component_name = 3;
  string component_description = 4;
  repeated ComponentInput inputs = 5;
  repeated ComponentOutput outputs = 6;
  optional MCPInfo mcp_info = 7;           // For MCP components
  string component_version = 8;
}

// Service definitions for tool management
service WorkflowToolService {
  // Get components that can be used as tools
  rpc GetToolCapableComponents(GetToolCapableComponentsRequest) returns (GetToolCapableComponentsResponse);
  
  // Generate tool schema for a component
  rpc GenerateToolSchema(GenerateToolSchemaRequest) returns (GenerateToolSchemaResponse);
  
  // Validate tool connections in a workflow
  rpc ValidateToolConnections(ValidateToolConnectionsRequest) returns (ValidateToolConnectionsResponse);
  
  // Get tool configuration for an agent
  rpc GetAgentToolConfiguration(GetAgentToolConfigurationRequest) returns (GetAgentToolConfigurationResponse);
}

// Request/Response messages
message GetToolCapableComponentsRequest {
  optional string category = 1;
  optional string agent_type = 2;
  bool include_mcp = 3;
  repeated string tags = 4;
}

message GetToolCapableComponentsResponse {
  repeated EnhancedComponent components = 1;
  int32 total_count = 2;
}

message GenerateToolSchemaRequest {
  string component_id = 1;
  string component_type = 2;
  optional string agent_id = 3;
  map<string, string> generation_options = 4;
}

message GenerateToolSchemaResponse {
  bool success = 1;
  string tool_schema_json = 2;
  string error_message = 3;
  ComponentToolSchema tool_schema = 4;
}

message ValidateToolConnectionsRequest {
  string workflow_id = 1;
  repeated ToolConnection tool_connections = 2;
}

message ValidateToolConnectionsResponse {
  bool is_valid = 1;
  repeated string validation_errors = 2;
  repeated string warnings = 3;
  int32 total_tool_connections = 4;
}

message GetAgentToolConfigurationRequest {
  string agent_id = 1;
  optional string workflow_id = 2;
  bool include_schemas = 3;
}

message GetAgentToolConfigurationResponse {
  bool success = 1;
  AgentToolConfiguration configuration = 2;
  string error_message = 3;
}
