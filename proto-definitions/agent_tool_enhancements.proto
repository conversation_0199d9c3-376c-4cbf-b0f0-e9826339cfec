// Optional enhancements to agent.proto for better tool integration support
// These are OPTIONAL and the current implementation works without them

syntax = "proto3";

package agent;

// Enhanced agent capabilities for tool integration
message AgentToolCapabilities {
  bool supports_workflow_tools = 1;        // Can use workflow components as tools
  bool supports_mcp_tools = 2;             // Can use MCP marketplace tools
  int32 max_concurrent_tools = 3;          // Maximum concurrent tool executions
  repeated string supported_tool_types = 4; // Supported component types
  bool requires_tool_approval = 5;         // Whether tool usage requires approval
  string tool_execution_mode = 6;          // "sequential", "parallel", "adaptive"
}

// Tool usage statistics
message AgentToolUsageStats {
  string agent_id = 1;
  int32 total_tool_calls = 2;
  int32 successful_tool_calls = 3;
  int32 failed_tool_calls = 4;
  double average_execution_time = 5;
  repeated ToolUsageStat tool_stats = 6;
  string last_updated = 7;
}

// Individual tool usage statistics
message ToolUsageStat {
  string tool_id = 1;
  string tool_type = 2;
  int32 call_count = 3;
  int32 success_count = 4;
  int32 error_count = 5;
  double average_execution_time = 6;
  string last_used = 7;
}

// Enhanced agent message (extends existing Agent)
message EnhancedAgent {
  // All existing Agent fields would be here
  string id = 1;
  string name = 2;
  string description = 3;
  // ... other agent fields
  repeated string workflow_ids = 17;
  repeated string mcp_server_ids = 18;
  
  // New tool-specific fields
  optional AgentToolCapabilities tool_capabilities = 29;
  repeated string connected_tool_ids = 30;
  int32 active_tool_connections = 31;
  optional AgentToolUsageStats tool_usage_stats = 32;
  string tool_configuration_version = 33;
}

// Agent execution context with tools
message AgentExecutionContext {
  string agent_id = 1;
  string execution_id = 2;
  string workflow_id = 3;
  repeated AvailableTool available_tools = 4;
  map<string, string> execution_config = 5;
  string created_at = 6;
}

// Available tool for agent execution
message AvailableTool {
  string tool_id = 1;
  string tool_name = 2;
  string tool_type = 3;
  string tool_schema_json = 4;
  bool is_enabled = 5;
  map<string, string> tool_metadata = 6;
}

// Tool execution request
message ToolExecutionRequest {
  string agent_id = 1;
  string execution_id = 2;
  string tool_id = 3;
  string tool_parameters_json = 4;
  string request_id = 5;
  string created_at = 6;
}

// Tool execution response
message ToolExecutionResponse {
  string request_id = 1;
  bool success = 2;
  string result_json = 3;
  string error_message = 4;
  double execution_time = 5;
  string completed_at = 6;
}

// Service definitions for agent tool management
service AgentToolService {
  // Configure tools for an agent
  rpc ConfigureAgentTools(ConfigureAgentToolsRequest) returns (ConfigureAgentToolsResponse);
  
  // Execute a tool for an agent
  rpc ExecuteTool(ToolExecutionRequest) returns (ToolExecutionResponse);
  
  // Get agent tool capabilities
  rpc GetAgentToolCapabilities(GetAgentToolCapabilitiesRequest) returns (GetAgentToolCapabilitiesResponse);
  
  // Get agent tool usage statistics
  rpc GetAgentToolUsageStats(GetAgentToolUsageStatsRequest) returns (GetAgentToolUsageStatsResponse);
  
  // Validate agent tool configuration
  rpc ValidateAgentToolConfiguration(ValidateAgentToolConfigurationRequest) returns (ValidateAgentToolConfigurationResponse);
}

// Request/Response messages
message ConfigureAgentToolsRequest {
  string agent_id = 1;
  repeated string tool_ids = 2;
  map<string, string> tool_configurations = 3;
  bool validate_compatibility = 4;
}

message ConfigureAgentToolsResponse {
  bool success = 1;
  string configuration_id = 2;
  repeated string configured_tool_ids = 3;
  repeated string configuration_errors = 4;
}

message GetAgentToolCapabilitiesRequest {
  string agent_id = 1;
}

message GetAgentToolCapabilitiesResponse {
  AgentToolCapabilities capabilities = 1;
  repeated string supported_components = 2;
}

message GetAgentToolUsageStatsRequest {
  string agent_id = 1;
  optional string start_date = 2;
  optional string end_date = 3;
}

message GetAgentToolUsageStatsResponse {
  AgentToolUsageStats usage_stats = 1;
  repeated ToolUsageStat detailed_stats = 2;
}

message ValidateAgentToolConfigurationRequest {
  string agent_id = 1;
  repeated string tool_ids = 2;
  string workflow_id = 3;
}

message ValidateAgentToolConfigurationResponse {
  bool is_valid = 1;
  repeated string validation_errors = 2;
  repeated string warnings = 3;
  repeated string recommendations = 4;
}
