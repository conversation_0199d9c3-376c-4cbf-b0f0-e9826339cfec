"""
Performance Optimizer - Advanced performance optimization utilities
"""

import time
import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import gc

logger = logging.getLogger(__name__)


@dataclass
class OptimizationResult:
    """Result of performance optimization"""
    operation: str
    original_time: float
    optimized_time: float
    improvement_percentage: float
    memory_saved: float
    optimization_strategy: str
    
    def __post_init__(self):
        if self.original_time > 0:
            self.improvement_percentage = (
                (self.original_time - self.optimized_time) / self.original_time
            ) * 100
        else:
            self.improvement_percentage = 0.0


@dataclass
class PerformanceProfile:
    """Performance profiling data"""
    operation: str
    execution_time: float
    memory_usage: float
    cpu_usage: float
    bottlenecks: List[str]
    optimization_suggestions: List[str]


class PerformanceOptimizer:
    """
    Advanced performance optimizer for tool integration workflows.
    
    Provides optimization strategies for schema generation, tool extraction,
    and workflow processing operations.
    """
    
    def __init__(self):
        """Initialize the performance optimizer."""
        self.optimization_cache = {}
        self.performance_profiles = {}
        self.optimization_strategies = {
            "schema_generation": self._optimize_schema_generation,
            "tool_extraction": self._optimize_tool_extraction,
            "batch_processing": self._optimize_batch_processing,
            "memory_usage": self._optimize_memory_usage,
            "concurrent_processing": self._optimize_concurrent_processing
        }
        
        logger.info("PerformanceOptimizer initialized")
    
    async def optimize_operation(
        self, 
        operation_name: str, 
        operation_func: Callable,
        *args, 
        **kwargs
    ) -> OptimizationResult:
        """
        Optimize a specific operation using best available strategy.
        
        Args:
            operation_name: Name of the operation to optimize
            operation_func: Function to optimize
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            OptimizationResult with optimization details
        """
        # Profile original operation
        original_profile = await self._profile_operation(
            operation_name, operation_func, *args, **kwargs
        )
        
        # Determine best optimization strategy
        strategy = self._select_optimization_strategy(original_profile)
        
        # Apply optimization
        optimized_func = self.optimization_strategies.get(strategy, operation_func)
        optimized_profile = await self._profile_operation(
            f"{operation_name}_optimized", optimized_func, *args, **kwargs
        )
        
        # Calculate improvement
        memory_saved = original_profile.memory_usage - optimized_profile.memory_usage
        
        return OptimizationResult(
            operation=operation_name,
            original_time=original_profile.execution_time,
            optimized_time=optimized_profile.execution_time,
            improvement_percentage=0.0,  # Will be calculated in __post_init__
            memory_saved=memory_saved,
            optimization_strategy=strategy
        )
    
    async def _profile_operation(
        self, 
        operation_name: str, 
        operation_func: Callable,
        *args, 
        **kwargs
    ) -> PerformanceProfile:
        """
        Profile an operation to gather performance metrics.
        
        Args:
            operation_name: Name of the operation
            operation_func: Function to profile
            *args: Arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            PerformanceProfile with profiling data
        """
        # Get initial system state
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        initial_cpu = process.cpu_percent()
        
        # Execute operation
        start_time = time.time()
        
        try:
            if asyncio.iscoroutinefunction(operation_func):
                result = await operation_func(*args, **kwargs)
            else:
                result = operation_func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Error profiling operation {operation_name}: {str(e)}")
            raise
        
        end_time = time.time()
        
        # Get final system state
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        final_cpu = process.cpu_percent()
        
        execution_time = end_time - start_time
        memory_usage = final_memory - initial_memory
        cpu_usage = final_cpu - initial_cpu
        
        # Analyze bottlenecks
        bottlenecks = self._identify_bottlenecks(execution_time, memory_usage, cpu_usage)
        
        # Generate optimization suggestions
        suggestions = self._generate_optimization_suggestions(bottlenecks)
        
        profile = PerformanceProfile(
            operation=operation_name,
            execution_time=execution_time,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage,
            bottlenecks=bottlenecks,
            optimization_suggestions=suggestions
        )
        
        self.performance_profiles[operation_name] = profile
        return profile
    
    def _select_optimization_strategy(self, profile: PerformanceProfile) -> str:
        """
        Select the best optimization strategy based on performance profile.
        
        Args:
            profile: Performance profile data
            
        Returns:
            Name of the best optimization strategy
        """
        # Analyze bottlenecks to select strategy
        if "high_memory_usage" in profile.bottlenecks:
            return "memory_usage"
        elif "slow_execution" in profile.bottlenecks:
            if "schema_generation" in profile.operation:
                return "schema_generation"
            elif "tool_extraction" in profile.operation:
                return "tool_extraction"
            else:
                return "concurrent_processing"
        elif "batch_processing_opportunity" in profile.bottlenecks:
            return "batch_processing"
        else:
            return "concurrent_processing"
    
    def _identify_bottlenecks(
        self, 
        execution_time: float, 
        memory_usage: float, 
        cpu_usage: float
    ) -> List[str]:
        """
        Identify performance bottlenecks based on metrics.
        
        Args:
            execution_time: Execution time in seconds
            memory_usage: Memory usage in MB
            cpu_usage: CPU usage percentage
            
        Returns:
            List of identified bottlenecks
        """
        bottlenecks = []
        
        # Time-based bottlenecks
        if execution_time > 0.1:  # >100ms
            bottlenecks.append("slow_execution")
        
        if execution_time > 0.05:  # >50ms
            bottlenecks.append("batch_processing_opportunity")
        
        # Memory-based bottlenecks
        if memory_usage > 10:  # >10MB
            bottlenecks.append("high_memory_usage")
        
        if memory_usage > 5:  # >5MB
            bottlenecks.append("memory_optimization_opportunity")
        
        # CPU-based bottlenecks
        if cpu_usage > 50:  # >50% CPU
            bottlenecks.append("high_cpu_usage")
        
        return bottlenecks
    
    def _generate_optimization_suggestions(self, bottlenecks: List[str]) -> List[str]:
        """
        Generate optimization suggestions based on bottlenecks.
        
        Args:
            bottlenecks: List of identified bottlenecks
            
        Returns:
            List of optimization suggestions
        """
        suggestions = []
        
        if "slow_execution" in bottlenecks:
            suggestions.extend([
                "Consider implementing caching for repeated operations",
                "Use batch processing for multiple similar operations",
                "Implement concurrent processing for independent operations"
            ])
        
        if "high_memory_usage" in bottlenecks:
            suggestions.extend([
                "Implement object pooling for frequently created objects",
                "Use generators instead of lists for large datasets",
                "Clear unused references and call garbage collection"
            ])
        
        if "batch_processing_opportunity" in bottlenecks:
            suggestions.extend([
                "Combine multiple operations into batch processing",
                "Use vectorized operations where possible",
                "Implement pipeline processing for sequential operations"
            ])
        
        if "high_cpu_usage" in bottlenecks:
            suggestions.extend([
                "Use async/await for I/O bound operations",
                "Implement worker pools for CPU-intensive tasks",
                "Consider algorithm optimization for computational bottlenecks"
            ])
        
        return suggestions
    
    async def _optimize_schema_generation(self, *args, **kwargs):
        """Optimize schema generation operations."""
        # Implement schema generation caching
        cache_key = str(args) + str(kwargs)
        
        if cache_key in self.optimization_cache:
            return self.optimization_cache[cache_key]
        
        # Use concurrent processing for multiple schemas
        if len(args) > 1 and isinstance(args[0], list):
            components = args[0]
            
            # Process schemas concurrently
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = [
                    executor.submit(self._generate_single_schema, component)
                    for component in components
                ]
                
                results = []
                for future in as_completed(futures):
                    results.append(future.result())
            
            self.optimization_cache[cache_key] = results
            return results
        
        # Single schema generation with optimization
        result = self._generate_single_schema(args[0] if args else {})
        self.optimization_cache[cache_key] = result
        return result
    
    def _generate_single_schema(self, component: Dict[str, Any]) -> Dict[str, Any]:
        """Generate optimized schema for a single component."""
        # Optimized schema generation logic
        schema = {
            "name": component.get("component_id", "unknown").replace("-", "_"),
            "description": component.get("component_name", "Component tool"),
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
        
        # Optimize parameter processing
        inputs = component.get("inputs", [])
        if inputs:
            # Use dictionary comprehension for better performance
            schema["parameters"]["properties"] = {
                inp["name"]: {
                    "type": inp.get("type", "string"),
                    "description": f"Input parameter {inp['name']}"
                }
                for inp in inputs
            }
            schema["parameters"]["required"] = [inp["name"] for inp in inputs]
        
        return schema
    
    async def _optimize_tool_extraction(self, *args, **kwargs):
        """Optimize tool extraction operations."""
        # Implement parallel tool extraction
        workflow = args[0] if args else {}
        
        # Find AgenticAI nodes
        agentic_nodes = [
            node for node in workflow.get("nodes", [])
            if node.get("type") == "AgenticAI"
        ]
        
        # Process each AgenticAI node concurrently
        tasks = [
            self._extract_tools_for_node_optimized(node, workflow)
            for node in agentic_nodes
        ]
        
        if tasks:
            results = await asyncio.gather(*tasks)
            
            # Update nodes with extracted tools
            for i, node in enumerate(agentic_nodes):
                if "data" not in node:
                    node["data"] = {}
                node["data"]["tools"] = results[i]
        
        return workflow
    
    async def _extract_tools_for_node_optimized(
        self, 
        agentic_node: Dict[str, Any], 
        workflow: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Extract tools for a node with optimization."""
        tools = []
        
        # Use set for faster lookup
        node_ids = {node["id"]: node for node in workflow.get("nodes", [])}
        
        # Process tool connections efficiently
        for edge in workflow.get("edges", []):
            if (edge["target"] == agentic_node["id"] and 
                edge.get("targetHandle", "").startswith("tool_")):
                
                source_node = node_ids.get(edge["source"])
                if source_node:
                    tool = self._create_tool_from_node_optimized(source_node)
                    tools.append(tool)
        
        return tools
    
    def _create_tool_from_node_optimized(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Create optimized tool definition from node."""
        component_type = node["type"]
        
        # Pre-computed tool type mapping
        tool_type = "mcp_marketplace" if component_type == "MCPMarketplace" else "workflow_component"
        
        # Optimized component creation
        component = {
            "component_id": node["id"],
            "component_type": component_type,
            "component_name": f"{component_type} Tool",
            "component_schema": self._generate_single_schema({
                "component_id": node["id"],
                "component_name": f"{component_type} Tool",
                "inputs": [{"name": "input", "type": "string"}]
            })
        }
        
        # Add MCP metadata if needed
        if component_type == "MCPMarketplace":
            component["mcp_metadata"] = {
                "server_url": "http://localhost:8000",
                "tool_name": f"mcp_{node['id']}"
            }
        
        return {
            "tool_type": tool_type,
            "component": component
        }
    
    async def _optimize_batch_processing(self, *args, **kwargs):
        """Optimize batch processing operations."""
        # Implement efficient batch processing
        items = args[0] if args and isinstance(args[0], list) else []
        
        if not items:
            return []
        
        # Process in optimized batches
        batch_size = min(10, len(items))  # Optimal batch size
        results = []
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            batch_results = await self._process_batch_optimized(batch)
            results.extend(batch_results)
        
        return results
    
    async def _process_batch_optimized(self, batch: List[Any]) -> List[Any]:
        """Process a batch of items with optimization."""
        # Use asyncio.gather for concurrent processing
        tasks = [self._process_single_item_optimized(item) for item in batch]
        return await asyncio.gather(*tasks)
    
    async def _process_single_item_optimized(self, item: Any) -> Any:
        """Process a single item with optimization."""
        # Simulate optimized processing
        await asyncio.sleep(0.001)  # Minimal processing time
        return {"processed": True, "item": item}
    
    async def _optimize_memory_usage(self, *args, **kwargs):
        """Optimize memory usage operations."""
        # Force garbage collection
        gc.collect()
        
        # Process with memory optimization
        result = await self._process_with_memory_optimization(*args, **kwargs)
        
        # Clean up after processing
        gc.collect()
        
        return result
    
    async def _process_with_memory_optimization(self, *args, **kwargs):
        """Process with memory optimization techniques."""
        # Use generators instead of lists where possible
        # Implement object pooling
        # Clear references after use
        
        # Simulate memory-optimized processing
        await asyncio.sleep(0.005)
        return {"optimized": True, "memory_efficient": True}
    
    async def _optimize_concurrent_processing(self, *args, **kwargs):
        """Optimize using concurrent processing."""
        # Implement concurrent processing optimization
        tasks = []
        
        # Create concurrent tasks based on input
        if args and isinstance(args[0], list):
            items = args[0]
            
            # Process items concurrently
            semaphore = asyncio.Semaphore(5)  # Limit concurrent operations
            
            async def process_with_semaphore(item):
                async with semaphore:
                    return await self._process_single_item_optimized(item)
            
            tasks = [process_with_semaphore(item) for item in items]
            results = await asyncio.gather(*tasks)
            return results
        
        # Single item processing
        return await self._process_single_item_optimized(args[0] if args else {})
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """
        Get comprehensive optimization report.
        
        Returns:
            Dictionary containing optimization statistics and recommendations
        """
        return {
            "total_operations_profiled": len(self.performance_profiles),
            "cache_hit_ratio": len(self.optimization_cache) / max(1, len(self.performance_profiles)),
            "performance_profiles": {
                name: {
                    "execution_time": profile.execution_time,
                    "memory_usage": profile.memory_usage,
                    "bottlenecks": profile.bottlenecks,
                    "suggestions": profile.optimization_suggestions
                }
                for name, profile in self.performance_profiles.items()
            },
            "optimization_strategies_used": list(self.optimization_strategies.keys()),
            "recommendations": self._generate_global_recommendations()
        }
    
    def _generate_global_recommendations(self) -> List[str]:
        """Generate global optimization recommendations."""
        recommendations = []
        
        # Analyze all profiles for global patterns
        total_profiles = len(self.performance_profiles)
        if total_profiles == 0:
            return ["No performance data available for analysis"]
        
        # Memory usage analysis
        high_memory_count = sum(
            1 for profile in self.performance_profiles.values()
            if profile.memory_usage > 5
        )
        
        if high_memory_count / total_profiles > 0.5:
            recommendations.append("Consider implementing global memory optimization strategies")
        
        # Execution time analysis
        slow_operations = sum(
            1 for profile in self.performance_profiles.values()
            if profile.execution_time > 0.05
        )
        
        if slow_operations / total_profiles > 0.3:
            recommendations.append("Consider implementing global caching and batch processing")
        
        # Cache effectiveness
        if len(self.optimization_cache) < total_profiles * 0.2:
            recommendations.append("Increase caching coverage for better performance")
        
        return recommendations
    
    def clear_cache(self) -> None:
        """Clear optimization cache."""
        self.optimization_cache.clear()
        logger.info("Optimization cache cleared")
    
    def reset_profiles(self) -> None:
        """Reset performance profiles."""
        self.performance_profiles.clear()
        logger.info("Performance profiles reset")


# Global optimizer instance
_performance_optimizer = None


def get_performance_optimizer() -> PerformanceOptimizer:
    """
    Get or create the global performance optimizer instance.
    
    Returns:
        The performance optimizer instance
    """
    global _performance_optimizer
    if _performance_optimizer is None:
        logger.info("Creating new global PerformanceOptimizer instance")
        _performance_optimizer = PerformanceOptimizer()
    return _performance_optimizer
