#!/usr/bin/env python3

import sys
import json
import os

# Add the workflow-service to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'workflow-service'))

def test_input_data_cleanup():
    """Test if tool-related input_data entries are properly excluded"""
    print("🔍 Testing input_data cleanup for tool connections...")
    
    try:
        # Load the agentic schema
        with open('agentic_schema.json', 'r') as f:
            agentic_schema = json.load(f)
        
        workflow_data = agentic_schema.get('workflow_data', {})
        
        # Import the conversion function
        from app.services.workflow_builder.workflow_schema_converter import convert_workflow_to_transition_schema
        
        # Convert to transition schema
        result = convert_workflow_to_transition_schema(workflow_data)
        
        print(f"\n🎉 CONVERSION SUCCESSFUL!")
        
        # Find the agent transition
        agent_transition = None
        for transition in result['transitions']:
            if transition['execution_type'] == 'agent':
                agent_transition = transition
                break
        
        if not agent_transition:
            print("❌ No agent transition found!")
            return False
        
        print(f"✅ Found agent transition: {agent_transition['id']}")
        
        # Check input_data for problematic entries
        input_data = agent_transition['node_info']['input_data']
        
        print(f"\n🔍 AGENT INPUT_DATA ANALYSIS:")
        print(f"   - Total input_data entries: {len(input_data)}")
        
        # Check for tool-related input_data entries
        problematic_entries = []
        
        for i, entry in enumerate(input_data):
            from_transition_id = entry.get('from_transition_id', '')
            handle_mappings = entry.get('handle_mappings', [])
            
            print(f"   - Entry {i+1}: {from_transition_id}")
            
            # Check if this entry has tool-related handle mappings
            for mapping in handle_mappings:
                target_handle_id = mapping.get('target_handle_id', '')
                source_transition_id = mapping.get('source_transition_id', '')
                
                if target_handle_id == 'tools':
                    problematic_entries.append({
                        'entry_index': i,
                        'from_transition_id': from_transition_id,
                        'source_transition_id': source_transition_id,
                        'target_handle_id': target_handle_id,
                        'mapping': mapping
                    })
                    print(f"     ❌ PROBLEM: Tool-related mapping found!")
                    print(f"        - Source: {source_transition_id}")
                    print(f"        - Target Handle: {target_handle_id}")
                else:
                    print(f"     ✅ Legitimate mapping: {target_handle_id}")
        
        print(f"\n📋 VALIDATION RESULTS:")
        
        if problematic_entries:
            print(f"❌ PROBLEM: Found {len(problematic_entries)} tool-related input_data entries:")
            for entry in problematic_entries:
                print(f"   - Entry {entry['entry_index']}: {entry['from_transition_id']}")
                print(f"     Target Handle: {entry['target_handle_id']}")
                print(f"     Source Transition: {entry['source_transition_id']}")
            return False
        else:
            print(f"✅ SUCCESS: No tool-related input_data entries found!")
        
        # Check that agent configuration still has tools
        tools_to_use = agent_transition['node_info']['tools_to_use']
        for tool in tools_to_use:
            if tool['tool_name'] == 'AgenticAI':
                tool_params = tool['tool_params']['items']
                for item in tool_params:
                    if item['field_name'] == 'agent_config':
                        agent_config = item['field_value']
                        if 'agent_tools' in agent_config:
                            tool_count = len(agent_config['agent_tools'])
                            print(f"✅ Agent configuration contains {tool_count} integrated tools")
                        else:
                            print(f"❌ Agent configuration missing agent_tools")
                        break
                break
        
        return len(problematic_entries) == 0
        
    except Exception as e:
        print(f"❌ Error in input_data cleanup test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting input_data cleanup test...")
    print("=" * 60)
    
    success = test_input_data_cleanup()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 INPUT_DATA CLEANUP TEST PASSED!")
        print("   ✅ No tool-related input_data entries found")
        print("   ✅ Agent receives only legitimate workflow inputs")
        print("   ✅ Tools properly integrated into agent configuration")
        print("   ✅ Architectural compliance maintained")
    else:
        print("❌ INPUT_DATA CLEANUP TEST FAILED!")
        print("   ❌ Tool-related input_data entries still present")
        print("   ❌ Architecture violation: tools should be callable functions, not input sources")
