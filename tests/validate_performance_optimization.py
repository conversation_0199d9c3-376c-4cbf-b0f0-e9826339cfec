#!/usr/bin/env python3
"""
Validate Performance Optimization & Benchmarking
Validation script for performance optimization and advanced benchmarking utilities
"""

import sys
import os
import asyncio
import time

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'workflow-service'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'workflow-service', 'app'))

def test_performance_optimizer_import():
    """Test performance optimizer import"""
    print("🧪 Testing Performance Optimizer Import...")
    
    try:
        from utils.performance.performance_optimizer import (
            PerformanceOptimizer,
            OptimizationResult,
            PerformanceProfile,
            get_performance_optimizer
        )
        
        # Test instance creation
        optimizer = get_performance_optimizer()
        assert optimizer is not None
        assert isinstance(optimizer, PerformanceOptimizer)
        
        print("   ✅ Performance optimizer imported and instantiated successfully")
        return True
    except Exception as e:
        print(f"   ❌ Performance optimizer import failed: {e}")
        return False

def test_advanced_benchmarking_import():
    """Test advanced benchmarking import"""
    print("🧪 Testing Advanced Benchmarking Import...")
    
    try:
        from utils.performance.advanced_benchmarking import (
            AdvancedBenchmarking,
            BenchmarkMetrics,
            PerformanceTarget,
            LoadTestResult,
            get_advanced_benchmarking
        )
        
        # Test instance creation
        benchmarking = get_advanced_benchmarking()
        assert benchmarking is not None
        assert isinstance(benchmarking, AdvancedBenchmarking)
        
        print("   ✅ Advanced benchmarking imported and instantiated successfully")
        return True
    except Exception as e:
        print(f"   ❌ Advanced benchmarking import failed: {e}")
        return False

async def test_performance_optimizer_functionality():
    """Test performance optimizer functionality"""
    print("🧪 Testing Performance Optimizer Functionality...")
    
    try:
        from utils.performance.performance_optimizer import get_performance_optimizer
        
        optimizer = get_performance_optimizer()
        
        # Test simple operation optimization
        async def test_operation():
            await asyncio.sleep(0.01)
            return {"result": "success"}
        
        # Profile the operation
        profile = await optimizer._profile_operation(
            "test_operation",
            test_operation
        )
        
        assert profile.operation == "test_operation"
        assert profile.execution_time > 0
        assert isinstance(profile.bottlenecks, list)
        assert isinstance(profile.optimization_suggestions, list)
        
        print(f"   ✅ Operation profiled: {profile.execution_time:.3f}s")
        print(f"   ✅ Bottlenecks identified: {len(profile.bottlenecks)}")
        print(f"   ✅ Suggestions generated: {len(profile.optimization_suggestions)}")
        
        return True
    except Exception as e:
        print(f"   ❌ Performance optimizer functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_advanced_benchmarking_functionality():
    """Test advanced benchmarking functionality"""
    print("🧪 Testing Advanced Benchmarking Functionality...")
    
    try:
        from utils.performance.advanced_benchmarking import get_advanced_benchmarking
        
        benchmarking = get_advanced_benchmarking()
        
        # Test simple benchmark
        async def test_operation():
            await asyncio.sleep(0.005)  # 5ms
            return {"benchmarked": True}
        
        # Run comprehensive benchmark
        metrics = await benchmarking.comprehensive_benchmark(
            "test_benchmark_operation",
            test_operation,
            iterations=10,
            warmup_iterations=2
        )
        
        assert metrics.operation == "test_benchmark_operation"
        assert metrics.iterations == 10
        assert metrics.average_time > 0
        assert metrics.success_rate > 0
        assert metrics.throughput > 0
        
        print(f"   ✅ Benchmark completed: {metrics.average_time:.3f}s avg")
        print(f"   ✅ Throughput: {metrics.throughput:.1f} ops/sec")
        print(f"   ✅ Success rate: {metrics.success_rate:.2%}")
        
        # Test performance target validation
        validation = benchmarking.validate_performance_targets(metrics)
        print(f"   ✅ Target validation: {validation.get('summary', 'No target defined')}")
        
        return True
    except Exception as e:
        print(f"   ❌ Advanced benchmarking functionality test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_optimization_strategies():
    """Test optimization strategies"""
    print("🧪 Testing Optimization Strategies...")
    
    try:
        from utils.performance.performance_optimizer import get_performance_optimizer
        
        optimizer = get_performance_optimizer()
        
        # Test schema generation optimization
        test_components = [
            {
                "component_id": f"test-component-{i}",
                "component_name": f"Test Component {i}",
                "inputs": [{"name": "input", "type": "string"}]
            }
            for i in range(5)
        ]
        
        # Test optimized schema generation
        start_time = time.time()
        result = await optimizer._optimize_schema_generation(test_components)
        end_time = time.time()
        
        execution_time = end_time - start_time
        
        assert isinstance(result, list)
        assert len(result) == 5
        assert execution_time < 0.1  # Should be fast
        
        print(f"   ✅ Schema generation optimization: {execution_time:.3f}s for 5 components")
        
        # Test batch processing optimization
        test_items = [{"item": i} for i in range(10)]
        
        start_time = time.time()
        batch_result = await optimizer._optimize_batch_processing(test_items)
        end_time = time.time()
        
        batch_time = end_time - start_time
        
        assert isinstance(batch_result, list)
        assert len(batch_result) == 10
        
        print(f"   ✅ Batch processing optimization: {batch_time:.3f}s for 10 items")
        
        return True
    except Exception as e:
        print(f"   ❌ Optimization strategies test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_load_testing():
    """Test load testing functionality"""
    print("🧪 Testing Load Testing Functionality...")
    
    try:
        from utils.performance.advanced_benchmarking import get_advanced_benchmarking
        
        benchmarking = get_advanced_benchmarking()
        
        # Define simple operation for load testing
        async def load_test_operation():
            await asyncio.sleep(0.01)  # 10ms operation
            return {"load_tested": True}
        
        # Run load test
        load_result = await benchmarking.load_test(
            "load_test_operation",
            load_test_operation,
            concurrent_users=3,
            duration=2.0  # 2 seconds
        )
        
        assert load_result.operation == "load_test_operation"
        assert load_result.concurrent_users == 3
        assert load_result.total_requests > 0
        assert load_result.requests_per_second > 0
        assert load_result.error_rate >= 0
        
        print(f"   ✅ Load test completed:")
        print(f"       Total requests: {load_result.total_requests}")
        print(f"       Requests/sec: {load_result.requests_per_second:.1f}")
        print(f"       Error rate: {load_result.error_rate:.2%}")
        print(f"       Avg response: {load_result.average_response_time:.3f}s")
        
        return True
    except Exception as e:
        print(f"   ❌ Load testing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_performance_reporting():
    """Test performance reporting"""
    print("🧪 Testing Performance Reporting...")
    
    try:
        from utils.performance.advanced_benchmarking import get_advanced_benchmarking
        
        benchmarking = get_advanced_benchmarking()
        
        # Run a few benchmarks to populate data
        async def report_test_operation():
            await asyncio.sleep(0.008)
            return {"reported": True}
        
        # Run multiple benchmarks
        for i in range(3):
            await benchmarking.comprehensive_benchmark(
                f"report_test_operation_{i}",
                report_test_operation,
                iterations=5
            )
        
        # Generate performance report
        report = benchmarking.generate_performance_report()
        
        assert "generated_at" in report
        assert "operations" in report
        assert "summary" in report
        assert report["summary"]["total_operations"] >= 3
        
        print(f"   ✅ Performance report generated:")
        print(f"       Operations: {report['summary']['total_operations']}")
        print(f"       Benchmarks: {report['summary']['total_benchmarks']}")
        
        # Test export functionality
        export_filename = "test_performance_export.json"
        benchmarking.export_benchmark_data(export_filename)
        
        # Check if file was created
        if os.path.exists(export_filename):
            print(f"   ✅ Benchmark data exported to {export_filename}")
            os.remove(export_filename)  # Clean up
        else:
            print(f"   ⚠️  Export file not found: {export_filename}")
        
        return True
    except Exception as e:
        print(f"   ❌ Performance reporting test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_regression_detection():
    """Test performance regression detection"""
    print("🧪 Testing Performance Regression Detection...")
    
    try:
        from utils.performance.advanced_benchmarking import get_advanced_benchmarking
        
        benchmarking = get_advanced_benchmarking()
        
        # Define baseline operation
        async def baseline_operation():
            await asyncio.sleep(0.01)  # 10ms
            return {"baseline": True}
        
        # Define regressed operation
        async def regressed_operation():
            await asyncio.sleep(0.02)  # 20ms - 100% slower
            return {"regressed": True}
        
        # Establish baseline
        baseline_metrics = await benchmarking.comprehensive_benchmark(
            "regression_detection_test",
            baseline_operation,
            iterations=10
        )
        
        # Test with regressed performance
        regressed_metrics = await benchmarking.comprehensive_benchmark(
            "regression_detection_test",
            regressed_operation,
            iterations=10
        )
        
        # Detect regression
        regression_analysis = benchmarking.detect_performance_regression(
            "regression_detection_test",
            regressed_metrics,
            regression_threshold=0.2  # 20% threshold
        )
        
        print(f"   ✅ Regression analysis:")
        print(f"       Has baseline: {regression_analysis['has_baseline']}")
        print(f"       Has regression: {regression_analysis['has_regression']}")
        print(f"       Time change: {regression_analysis['time_change']:.1%}")
        
        # Should detect regression since we doubled the time
        assert regression_analysis["has_regression"] == True
        assert regression_analysis["time_change"] > 0.2
        
        return True
    except Exception as e:
        print(f"   ❌ Regression detection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main validation function"""
    print("=" * 70)
    print("🚀 Performance Optimization & Benchmarking Validation")
    print("=" * 70)
    
    tests = [
        ("Performance Optimizer Import", test_performance_optimizer_import),
        ("Advanced Benchmarking Import", test_advanced_benchmarking_import),
        ("Performance Optimizer Functionality", test_performance_optimizer_functionality),
        ("Advanced Benchmarking Functionality", test_advanced_benchmarking_functionality),
        ("Optimization Strategies", test_optimization_strategies),
        ("Load Testing", test_load_testing),
        ("Performance Reporting", test_performance_reporting),
        ("Regression Detection", test_regression_detection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} Test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"   ✅ {test_name} test PASSED")
            else:
                print(f"   ❌ {test_name} test FAILED")
        except Exception as e:
            print(f"   ❌ {test_name} test FAILED with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL PERFORMANCE OPTIMIZATION TESTS PASSED!")
        print("\n🚀 Performance Optimization & Benchmarking is READY for:")
        print("   ✅ Advanced performance optimization strategies")
        print("   ✅ Comprehensive benchmarking with detailed metrics")
        print("   ✅ Load testing with concurrent user simulation")
        print("   ✅ Performance target validation and compliance")
        print("   ✅ Performance regression detection and analysis")
        print("   ✅ Detailed performance reporting and export")
        print("\n🎯 Ready for production performance monitoring!")
        return True
    else:
        print("❌ Some performance optimization tests failed")
        print("   Please fix the issues before proceeding")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
