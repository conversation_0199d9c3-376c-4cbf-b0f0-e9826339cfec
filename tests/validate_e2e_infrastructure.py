#!/usr/bin/env python3
"""
Validate E2E Test Infrastructure
Simple validation script to ensure E2E test infrastructure is working
"""

import sys
import os
import asyncio

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'workflow-service'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'workflow-service', 'app'))

def test_imports():
    """Test that all required modules can be imported"""
    print("🧪 Testing E2E Infrastructure Imports...")
    
    try:
        # Test utils imports
        from utils.test_helpers import (
            create_test_workflow,
            create_test_component,
            create_test_agent_config,
            MockWorkflowService,
            MockOrchestrationEngine,
            MockAgentService,
            PerformanceBenchmark
        )
        print("   ✅ Test helpers imported successfully")
        
        return True
    except Exception as e:
        print(f"   ❌ Import failed: {e}")
        return False

def test_component_creation():
    """Test component creation utilities"""
    print("🧪 Testing Component Creation...")
    
    try:
        from utils.test_helpers import create_test_component
        
        component = create_test_component(
            component_id="test-component-1",
            component_type="TestComponent",
            component_name="Test Component",
            inputs=[{"name": "input", "type": "string"}],
            outputs=[{"name": "output", "type": "object"}]
        )
        
        assert component["component_id"] == "test-component-1"
        assert component["component_type"] == "TestComponent"
        assert "component_schema" in component
        
        print("   ✅ Component creation working")
        return True
    except Exception as e:
        print(f"   ❌ Component creation failed: {e}")
        return False

def test_workflow_creation():
    """Test workflow creation utilities"""
    print("🧪 Testing Workflow Creation...")
    
    try:
        from utils.test_helpers import create_test_workflow
        
        workflow = create_test_workflow(
            workflow_id="test-workflow-1",
            nodes=[
                {
                    "id": "node-1",
                    "type": "TestNode",
                    "position": {"x": 100, "y": 100}
                }
            ],
            edges=[
                {
                    "id": "edge-1",
                    "source": "node-1",
                    "target": "node-2"
                }
            ]
        )
        
        assert workflow["id"] == "test-workflow-1"
        assert len(workflow["nodes"]) == 1
        assert len(workflow["edges"]) == 1
        
        print("   ✅ Workflow creation working")
        return True
    except Exception as e:
        print(f"   ❌ Workflow creation failed: {e}")
        return False

async def test_mock_services():
    """Test mock services functionality"""
    print("🧪 Testing Mock Services...")
    
    try:
        from utils.test_helpers import (
            MockWorkflowService,
            MockOrchestrationEngine,
            MockAgentService,
            create_test_component,
            create_test_workflow
        )
        
        # Test workflow service
        workflow_service = MockWorkflowService()
        component = create_test_component(
            component_id="mock-test-1",
            component_type="MockTest",
            component_name="Mock Test Component"
        )
        
        workflow_service.register_component(component)
        retrieved = workflow_service.get_component("mock-test-1")
        
        assert retrieved is not None
        assert retrieved["component_id"] == "mock-test-1"
        print("   ✅ Mock workflow service working")
        
        # Test orchestration engine
        orchestration_engine = MockOrchestrationEngine()
        workflow = create_test_workflow(
            workflow_id="mock-workflow-1",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Test query"}
                }
            ],
            edges=[]
        )
        
        processed = await orchestration_engine.process_workflow(workflow)
        assert processed is not None
        assert processed["id"] == "mock-workflow-1"
        print("   ✅ Mock orchestration engine working")
        
        # Test agent service
        agent_service = MockAgentService()
        agent_config = {
            "id": "test-agent-1",
            "name": "Test Agent",
            "tools": []
        }
        
        result = await agent_service.process_agent_config(agent_config)
        assert result["status"] == "success"
        assert result["agent_id"] == "test-agent-1"
        print("   ✅ Mock agent service working")
        
        return True
    except Exception as e:
        print(f"   ❌ Mock services failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_e2e_workflow():
    """Test a simple end-to-end workflow"""
    print("🧪 Testing Simple E2E Workflow...")
    
    try:
        from utils.test_helpers import (
            MockWorkflowService,
            MockOrchestrationEngine,
            MockAgentService,
            create_test_component,
            create_test_workflow
        )
        
        # Set up services
        workflow_service = MockWorkflowService()
        orchestration_engine = MockOrchestrationEngine()
        agent_service = MockAgentService()
        
        # Create and register a component
        component = create_test_component(
            component_id="simple-tool-1",
            component_type="SimpleTool",
            component_name="Simple Tool",
            inputs=[{"name": "input", "type": "string"}],
            outputs=[{"name": "output", "type": "object"}]
        )
        
        workflow_service.register_component(component)
        
        # Create workflow with tool connection
        workflow = create_test_workflow(
            workflow_id="simple-tool-workflow",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Use the simple tool"}
                },
                {
                    "id": "simple-tool-node",
                    "type": "SimpleTool",
                    "position": {"x": 500, "y": 100},
                    "data": {"input": "test data"}
                }
            ],
            edges=[
                {
                    "id": "tool-connection",
                    "source": "simple-tool-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_1"
                }
            ]
        )
        
        # Process workflow
        processed_workflow = await orchestration_engine.process_workflow(workflow)
        
        # Find AgenticAI node and verify tools
        agentic_node = None
        for node in processed_workflow["nodes"]:
            if node["type"] == "AgenticAI":
                agentic_node = node
                break
        
        assert agentic_node is not None
        assert "tools" in agentic_node["data"]
        assert len(agentic_node["data"]["tools"]) == 1
        
        tool = agentic_node["data"]["tools"][0]
        assert tool["tool_type"] == "workflow_component"
        assert tool["component"]["component_type"] == "SimpleTool"
        
        print("   ✅ Simple E2E workflow completed successfully")
        print("   ✅ Tool extraction working correctly")
        print("   ✅ Mock services integration successful")
        
        return True
    except Exception as e:
        print(f"   ❌ E2E workflow failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_utilities():
    """Test performance benchmark utilities"""
    print("🧪 Testing Performance Utilities...")
    
    try:
        from utils.test_helpers import PerformanceBenchmark
        
        benchmark = PerformanceBenchmark()
        assert benchmark is not None
        
        # Test benchmark stats
        stats = benchmark.get_all_stats()
        assert isinstance(stats, dict)
        
        print("   ✅ Performance benchmark utility working")
        return True
    except Exception as e:
        print(f"   ❌ Performance utilities failed: {e}")
        return False

async def main():
    """Main validation function"""
    print("=" * 60)
    print("🚀 E2E Test Infrastructure Validation")
    print("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Component Creation", test_component_creation),
        ("Workflow Creation", test_workflow_creation),
        ("Mock Services", test_mock_services),
        ("Simple E2E Workflow", test_simple_e2e_workflow),
        ("Performance Utilities", test_performance_utilities)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} Test...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"   ✅ {test_name} test PASSED")
            else:
                print(f"   ❌ {test_name} test FAILED")
        except Exception as e:
            print(f"   ❌ {test_name} test FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL E2E INFRASTRUCTURE TESTS PASSED!")
        print("\n🚀 E2E Test Infrastructure is READY for:")
        print("   ✅ Complete workflow testing")
        print("   ✅ Multiple tool connections testing")
        print("   ✅ Mixed regular and MCP component testing")
        print("   ✅ Tool disconnection and reconnection testing")
        print("   ✅ Error scenarios and recovery testing")
        print("   ✅ Performance benchmarking")
        print("\n🎯 Ready to proceed with comprehensive E2E testing!")
        return True
    else:
        print("❌ Some E2E infrastructure tests failed")
        print("   Please fix the issues before proceeding")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
