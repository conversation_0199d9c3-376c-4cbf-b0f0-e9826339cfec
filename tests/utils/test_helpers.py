"""
Test Utilities and Helpers for E2E Integration Testing
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional
from unittest.mock import Mock, AsyncMock


def create_test_workflow(
    workflow_id: str,
    nodes: List[Dict[str, Any]],
    edges: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """Create a test workflow structure"""
    return {
        "id": workflow_id,
        "name": f"Test Workflow {workflow_id}",
        "nodes": nodes,
        "edges": edges,
        "metadata": {
            "created_at": time.time(),
            "version": "1.0.0"
        }
    }


def create_test_component(
    component_id: str,
    component_type: str,
    component_name: str,
    inputs: List[Dict[str, Any]] = None,
    outputs: List[Dict[str, Any]] = None,
    mcp_metadata: Dict[str, Any] = None
) -> Dict[str, Any]:
    """Create a test component structure"""
    component = {
        "component_id": component_id,
        "component_type": component_type,
        "component_name": component_name,
        "inputs": inputs or [],
        "outputs": outputs or [],
        "component_schema": {
            "name": component_id.replace("-", "_"),
            "description": f"{component_name} for testing",
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    }
    
    # Add input parameters to schema
    if inputs:
        for input_def in inputs:
            component["component_schema"]["parameters"]["properties"][input_def["name"]] = {
                "type": input_def["type"],
                "description": f"Input parameter {input_def['name']}"
            }
            component["component_schema"]["parameters"]["required"].append(input_def["name"])
    
    # Add MCP metadata if provided
    if mcp_metadata:
        component["mcp_metadata"] = mcp_metadata
    
    return component


def create_test_agent_config(
    agent_id: str,
    tools: List[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """Create a test agent configuration"""
    return {
        "id": agent_id,
        "name": f"Test Agent {agent_id}",
        "description": "Test agent for E2E testing",
        "system_message": "You are a helpful test agent",
        "model_provider": "OpenAI",
        "model_name": "gpt-4-turbo",
        "tools": tools or [],
        "execution_type": "response",
        "max_tokens": 1000,
        "temperature": 0.7
    }


class MockWorkflowService:
    """Mock workflow service for testing"""
    
    def __init__(self):
        self.components = {}
        self.workflows = {}
    
    def register_component(self, component: Dict[str, Any]) -> None:
        """Register a component"""
        self.components[component["component_id"]] = component
    
    def get_component(self, component_id: str) -> Optional[Dict[str, Any]]:
        """Get a component by ID"""
        return self.components.get(component_id)
    
    def get_all_components(self) -> List[Dict[str, Any]]:
        """Get all registered components"""
        return list(self.components.values())
    
    async def create_workflow(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """Create a workflow"""
        self.workflows[workflow["id"]] = workflow
        return {"status": "success", "workflow_id": workflow["id"]}
    
    async def get_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get a workflow by ID"""
        return self.workflows.get(workflow_id)


class MockOrchestrationEngine:
    """Mock orchestration engine for testing"""
    
    def __init__(self):
        self.processed_workflows = {}
        self.executions = {}
    
    async def process_workflow(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """Process a workflow and extract tool information"""
        processed_workflow = workflow.copy()
        
        # Find AgenticAI nodes and extract tools
        for node in processed_workflow["nodes"]:
            if node["type"] == "AgenticAI":
                tools = self._extract_tools_for_node(node, workflow)
                if "data" not in node:
                    node["data"] = {}
                node["data"]["tools"] = tools
        
        self.processed_workflows[workflow["id"]] = processed_workflow
        return processed_workflow
    
    def _extract_tools_for_node(self, agentic_node: Dict[str, Any], workflow: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract tools connected to an AgenticAI node"""
        tools = []
        
        # Find edges connecting to tool handles
        for edge in workflow["edges"]:
            if (edge["target"] == agentic_node["id"] and 
                edge["targetHandle"] and 
                edge["targetHandle"].startswith("tool_")):
                
                # Find source node
                source_node = None
                for node in workflow["nodes"]:
                    if node["id"] == edge["source"]:
                        source_node = node
                        break
                
                if source_node:
                    tool = self._create_tool_from_node(source_node)
                    tools.append(tool)
        
        return tools
    
    def _create_tool_from_node(self, node: Dict[str, Any]) -> Dict[str, Any]:
        """Create a tool definition from a workflow node"""
        component_type = node["type"]
        
        # Determine tool type
        if component_type == "MCPMarketplace":
            tool_type = "mcp_marketplace"
        else:
            tool_type = "workflow_component"
        
        # Create component definition
        component = {
            "component_id": node["id"],
            "component_type": component_type,
            "component_name": f"{component_type} Tool",
            "component_schema": {
                "name": node["id"].replace("-", "_"),
                "description": f"{component_type} tool for agent",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "input": {"type": "string", "description": "Input data"}
                    },
                    "required": ["input"]
                }
            }
        }
        
        # Add MCP metadata if it's an MCP component
        if component_type == "MCPMarketplace":
            component["mcp_metadata"] = {
                "server_url": "http://localhost:8000",
                "tool_name": f"mcp_{node['id']}"
            }
        
        return {
            "tool_type": tool_type,
            "component": component
        }
    
    async def execute_workflow(self, workflow_id: str, execution_id: str) -> Dict[str, Any]:
        """Execute a workflow"""
        # Simulate workflow execution
        await asyncio.sleep(0.1)  # Simulate processing time
        
        execution_result = {
            "status": "success",
            "workflow_id": workflow_id,
            "execution_id": execution_id,
            "tool_executions": [
                {
                    "tool_id": "tool_1",
                    "status": "success",
                    "result": {"output": "Tool 1 executed successfully"}
                },
                {
                    "tool_id": "tool_2",
                    "status": "success",
                    "result": {"output": "Tool 2 executed successfully"}
                }
            ],
            "execution_time": 0.5
        }
        
        self.executions[execution_id] = execution_result
        return execution_result


class MockAgentService:
    """Mock agent service for testing"""
    
    def __init__(self):
        self.agents = {}
        self.tool_functions = {}
    
    async def process_agent_config(self, agent_config: Dict[str, Any]) -> Dict[str, Any]:
        """Process agent configuration and create tool functions"""
        agent_id = agent_config["id"]
        
        # Create tool functions from tools
        tool_functions = []
        for tool in agent_config.get("tools", []):
            tool_function = self._create_tool_function(tool)
            tool_functions.append(tool_function)
        
        # Store agent and tool functions
        self.agents[agent_id] = agent_config
        self.tool_functions[agent_id] = tool_functions
        
        return {
            "status": "success",
            "agent_id": agent_id,
            "tool_functions": tool_functions
        }
    
    def _create_tool_function(self, tool: Dict[str, Any]) -> Dict[str, Any]:
        """Create a tool function from tool definition"""
        component = tool["component"]
        
        return {
            "name": component["component_schema"]["name"],
            "description": component["component_schema"]["description"],
            "parameters": component["component_schema"]["parameters"],
            "tool_type": tool["tool_type"],
            "component_id": component["component_id"]
        }
    
    async def execute_agent(self, agent_id: str, query: str) -> Dict[str, Any]:
        """Execute an agent with a query"""
        # Simulate agent execution
        await asyncio.sleep(0.2)
        
        return {
            "status": "success",
            "agent_id": agent_id,
            "query": query,
            "response": "Agent executed successfully with tools",
            "tool_calls": [
                {"tool_name": "tool_1", "result": "success"},
                {"tool_name": "tool_2", "result": "success"}
            ]
        }


class MockKafkaClient:
    """Mock Kafka client for testing"""
    
    def __init__(self):
        self.messages = []
        self.topics = {}
    
    async def send_message(self, topic: str, message: Dict[str, Any]) -> bool:
        """Send a message to a topic"""
        self.messages.append({
            "topic": topic,
            "message": message,
            "timestamp": time.time()
        })
        
        if topic not in self.topics:
            self.topics[topic] = []
        self.topics[topic].append(message)
        
        return True
    
    async def consume_messages(self, topic: str, timeout: float = 1.0) -> List[Dict[str, Any]]:
        """Consume messages from a topic"""
        await asyncio.sleep(0.1)  # Simulate network delay
        return self.topics.get(topic, [])
    
    def get_all_messages(self) -> List[Dict[str, Any]]:
        """Get all messages sent"""
        return self.messages
    
    def clear_messages(self) -> None:
        """Clear all messages"""
        self.messages.clear()
        self.topics.clear()


class PerformanceBenchmark:
    """Performance benchmarking utility for E2E tests"""
    
    def __init__(self):
        self.benchmarks = {}
    
    async def benchmark_operation(self, operation_name: str, operation_func, *args, **kwargs):
        """Benchmark an async operation"""
        start_time = time.time()
        
        try:
            result = await operation_func(*args, **kwargs)
            end_time = time.time()
            
            execution_time = end_time - start_time
            
            if operation_name not in self.benchmarks:
                self.benchmarks[operation_name] = []
            
            self.benchmarks[operation_name].append({
                "execution_time": execution_time,
                "status": "success",
                "timestamp": start_time
            })
            
            return result, execution_time
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            if operation_name not in self.benchmarks:
                self.benchmarks[operation_name] = []
            
            self.benchmarks[operation_name].append({
                "execution_time": execution_time,
                "status": "error",
                "error": str(e),
                "timestamp": start_time
            })
            
            raise
    
    def get_benchmark_stats(self, operation_name: str) -> Dict[str, Any]:
        """Get benchmark statistics for an operation"""
        if operation_name not in self.benchmarks:
            return {}
        
        benchmarks = self.benchmarks[operation_name]
        execution_times = [b["execution_time"] for b in benchmarks if b["status"] == "success"]
        
        if not execution_times:
            return {"operation": operation_name, "count": 0}
        
        return {
            "operation": operation_name,
            "count": len(execution_times),
            "average_time": sum(execution_times) / len(execution_times),
            "min_time": min(execution_times),
            "max_time": max(execution_times),
            "success_rate": len(execution_times) / len(benchmarks)
        }
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get all benchmark statistics"""
        return {op: self.get_benchmark_stats(op) for op in self.benchmarks.keys()}
