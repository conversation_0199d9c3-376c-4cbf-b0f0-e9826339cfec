"""
MCP Tool Integration Tests
Tests the integration of MCP marketplace components as tools in AgenticAI workflows
"""

import pytest
import asyncio
import json
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock, patch

# Import test utilities
from tests.utils.test_helpers import (
    create_test_workflow,
    create_test_component,
    create_test_agent_config,
    MockWorkflowService,
    MockOrchestrationEngine,
    MockAgentService,
    PerformanceBenchmark
)


class TestMCPToolIntegration:
    """Integration tests for MCP marketplace components as tools"""

    @pytest.fixture
    def mock_services(self):
        """Set up mock services for MCP testing"""
        return {
            "workflow_service": MockWorkflowService(),
            "orchestration_engine": MockOrchestrationEngine(),
            "agent_service": MockAgentService()
        }

    @pytest.fixture
    def mcp_components(self):
        """Create test MCP components"""
        return [
            create_test_component(
                component_id="mcp-weather-1",
                component_type="MCPMarketplace",
                component_name="Weather API Tool",
                inputs=[{"name": "location", "type": "string"}],
                outputs=[{"name": "weather_data", "type": "object"}],
                mcp_metadata={
                    "server_url": "http://localhost:8001",
                    "tool_name": "get_weather",
                    "server_type": "weather_api"
                }
            ),
            create_test_component(
                component_id="mcp-calculator-1",
                component_type="MCPMarketplace",
                component_name="Calculator Tool",
                inputs=[{"name": "expression", "type": "string"}],
                outputs=[{"name": "result", "type": "number"}],
                mcp_metadata={
                    "server_url": "http://localhost:8002",
                    "tool_name": "calculate",
                    "server_type": "calculator"
                }
            ),
            create_test_component(
                component_id="mcp-database-1",
                component_type="MCPMarketplace",
                component_name="Database Query Tool",
                inputs=[{"name": "query", "type": "string"}],
                outputs=[{"name": "results", "type": "array"}],
                mcp_metadata={
                    "server_url": "http://localhost:8003",
                    "tool_name": "execute_query",
                    "server_type": "database"
                }
            )
        ]

    @pytest.mark.asyncio
    async def test_single_mcp_tool_integration(self, mock_services, mcp_components):
        """Test integration of a single MCP tool"""
        print("\n🧪 Testing Single MCP Tool Integration")
        
        # Register MCP component
        weather_component = mcp_components[0]
        mock_services["workflow_service"].register_component(weather_component)
        
        # Create workflow with MCP tool
        workflow = create_test_workflow(
            workflow_id="single-mcp-test",
            nodes=[
                {
                    "id": "start-node",
                    "type": "StartNode",
                    "position": {"x": 100, "y": 100}
                },
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Get weather for New York"}
                },
                {
                    "id": "weather-tool-node",
                    "type": "MCPMarketplace",
                    "position": {"x": 500, "y": 100},
                    "data": {"location": "New York"}
                }
            ],
            edges=[
                {
                    "id": "start-to-agent",
                    "source": "start-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "input"
                },
                {
                    "id": "weather-tool-connection",
                    "source": "weather-tool-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_1"
                }
            ]
        )
        
        # Process workflow
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Verify MCP tool extraction
        agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")
        tools = agentic_node["data"]["tools"]
        
        assert len(tools) == 1
        mcp_tool = tools[0]
        
        # Verify MCP tool structure
        assert mcp_tool["tool_type"] == "mcp_marketplace"
        assert mcp_tool["component"]["component_type"] == "MCPMarketplace"
        assert "mcp_metadata" in mcp_tool["component"]
        
        # Verify MCP metadata
        mcp_metadata = mcp_tool["component"]["mcp_metadata"]
        assert mcp_metadata["server_url"] == "http://localhost:8001"
        assert mcp_metadata["tool_name"] == "get_weather"
        assert mcp_metadata["server_type"] == "weather_api"
        
        # Verify component schema
        schema = mcp_tool["component"]["component_schema"]
        assert schema["name"] == "mcp_weather_1"
        assert "location" in schema["parameters"]["properties"]
        
        print("   ✅ Single MCP tool integration successful")

    @pytest.mark.asyncio
    async def test_multiple_mcp_tools_integration(self, mock_services, mcp_components):
        """Test integration of multiple MCP tools"""
        print("\n🧪 Testing Multiple MCP Tools Integration")
        
        # Register all MCP components
        for component in mcp_components:
            mock_services["workflow_service"].register_component(component)
        
        # Create workflow with multiple MCP tools
        workflow_nodes = [
            {
                "id": "agentic-ai-node",
                "type": "AgenticAI",
                "position": {"x": 300, "y": 100},
                "data": {"query": "Use weather, calculator, and database tools"}
            }
        ]
        
        workflow_edges = []
        
        # Add MCP tool nodes and connections
        for i, component in enumerate(mcp_components):
            workflow_nodes.append({
                "id": f"mcp-tool-node-{i+1}",
                "type": "MCPMarketplace",
                "position": {"x": 500, "y": 50 + i * 50},
                "data": {"input": f"test data {i+1}"}
            })
            
            workflow_edges.append({
                "id": f"mcp-tool-connection-{i+1}",
                "source": f"mcp-tool-node-{i+1}",
                "target": "agentic-ai-node",
                "sourceHandle": "output",
                "targetHandle": f"tool_{i+1}"
            })
        
        workflow = create_test_workflow(
            workflow_id="multiple-mcp-test",
            nodes=workflow_nodes,
            edges=workflow_edges
        )
        
        # Process workflow
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Verify all MCP tools are extracted
        agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")
        tools = agentic_node["data"]["tools"]
        
        assert len(tools) == 3
        
        # Verify each MCP tool
        for i, tool in enumerate(tools):
            assert tool["tool_type"] == "mcp_marketplace"
            assert tool["component"]["component_type"] == "MCPMarketplace"
            assert "mcp_metadata" in tool["component"]
            
            # Verify unique server URLs
            server_url = tool["component"]["mcp_metadata"]["server_url"]
            assert server_url in ["http://localhost:8001", "http://localhost:8002", "http://localhost:8003"]
        
        print("   ✅ Multiple MCP tools integration successful")

    @pytest.mark.asyncio
    async def test_mixed_mcp_and_regular_tools(self, mock_services, mcp_components):
        """Test mixed MCP and regular workflow component tools"""
        print("\n🧪 Testing Mixed MCP and Regular Tools")
        
        # Register MCP component
        weather_component = mcp_components[0]
        mock_services["workflow_service"].register_component(weather_component)
        
        # Create regular component
        regular_component = create_test_component(
            component_id="data-processor-1",
            component_type="DataProcessor",
            component_name="Data Processing Tool",
            inputs=[{"name": "input_data", "type": "string"}],
            outputs=[{"name": "processed_data", "type": "object"}]
        )
        mock_services["workflow_service"].register_component(regular_component)
        
        # Create workflow with both types
        workflow = create_test_workflow(
            workflow_id="mixed-tools-test",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Use both MCP and regular tools"}
                },
                {
                    "id": "weather-tool-node",
                    "type": "MCPMarketplace",
                    "position": {"x": 500, "y": 50},
                    "data": {"location": "Boston"}
                },
                {
                    "id": "data-processor-node",
                    "type": "DataProcessor",
                    "position": {"x": 500, "y": 150},
                    "data": {"input_data": "test data"}
                }
            ],
            edges=[
                {
                    "id": "weather-tool-connection",
                    "source": "weather-tool-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_1"
                },
                {
                    "id": "data-processor-connection",
                    "source": "data-processor-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_2"
                }
            ]
        )
        
        # Process workflow
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Verify both tool types
        agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")
        tools = agentic_node["data"]["tools"]
        
        assert len(tools) == 2
        
        # Find MCP and regular tools
        mcp_tool = next(t for t in tools if t["tool_type"] == "mcp_marketplace")
        regular_tool = next(t for t in tools if t["tool_type"] == "workflow_component")
        
        # Verify MCP tool
        assert mcp_tool["component"]["component_type"] == "MCPMarketplace"
        assert "mcp_metadata" in mcp_tool["component"]
        
        # Verify regular tool
        assert regular_tool["component"]["component_type"] == "DataProcessor"
        assert "mcp_metadata" not in regular_tool["component"]
        
        print("   ✅ Mixed MCP and regular tools integration successful")

    @pytest.mark.asyncio
    async def test_mcp_tool_schema_generation(self, mock_services, mcp_components):
        """Test MCP tool schema generation and validation"""
        print("\n🧪 Testing MCP Tool Schema Generation")
        
        # Register calculator MCP component
        calculator_component = mcp_components[1]
        mock_services["workflow_service"].register_component(calculator_component)
        
        # Create workflow
        workflow = create_test_workflow(
            workflow_id="mcp-schema-test",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Calculate mathematical expressions"}
                },
                {
                    "id": "calculator-tool-node",
                    "type": "MCPMarketplace",
                    "position": {"x": 500, "y": 100},
                    "data": {"expression": "2 + 2"}
                }
            ],
            edges=[
                {
                    "id": "calculator-tool-connection",
                    "source": "calculator-tool-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_1"
                }
            ]
        )
        
        # Process workflow
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Extract tool schema
        agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")
        tool = agentic_node["data"]["tools"][0]
        schema = tool["component"]["component_schema"]
        
        # Verify schema structure
        assert schema["name"] == "mcp_calculator_1"
        assert "Calculate" in schema["description"]
        
        # Verify parameters
        parameters = schema["parameters"]
        assert parameters["type"] == "object"
        assert "expression" in parameters["properties"]
        assert parameters["properties"]["expression"]["type"] == "string"
        assert "expression" in parameters["required"]
        
        # Verify MCP-specific metadata preservation
        mcp_metadata = tool["component"]["mcp_metadata"]
        assert mcp_metadata["tool_name"] == "calculate"
        assert mcp_metadata["server_type"] == "calculator"
        
        print("   ✅ MCP tool schema generation successful")

    @pytest.mark.asyncio
    async def test_mcp_tool_performance(self, mock_services, mcp_components):
        """Test MCP tool integration performance"""
        print("\n🧪 Testing MCP Tool Performance")
        
        benchmark = PerformanceBenchmark()
        
        # Register all MCP components
        for component in mcp_components:
            mock_services["workflow_service"].register_component(component)
        
        # Create workflow with multiple MCP tools
        workflow = create_test_workflow(
            workflow_id="mcp-performance-test",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Performance test with MCP tools"}
                }
            ] + [
                {
                    "id": f"mcp-tool-{i+1}",
                    "type": "MCPMarketplace",
                    "position": {"x": 500, "y": 50 + i * 50},
                    "data": {"input": f"test {i+1}"}
                }
                for i in range(3)
            ],
            edges=[
                {
                    "id": f"mcp-connection-{i+1}",
                    "source": f"mcp-tool-{i+1}",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": f"tool_{i+1}"
                }
                for i in range(3)
            ]
        )
        
        # Benchmark MCP tool extraction
        _, extraction_time = await benchmark.benchmark_operation(
            "mcp_tool_extraction",
            mock_services["orchestration_engine"].process_workflow,
            workflow
        )
        
        # Verify performance targets
        assert extraction_time < 0.05, f"MCP tool extraction took {extraction_time:.3f}s, should be < 0.05s"
        
        # Benchmark agent config with MCP tools
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")
        tools = agentic_node["data"]["tools"]
        
        agent_config = create_test_agent_config("mcp-perf-agent", tools)
        
        _, agent_time = await benchmark.benchmark_operation(
            "mcp_agent_processing",
            mock_services["agent_service"].process_agent_config,
            agent_config
        )
        
        assert agent_time < 0.03, f"MCP agent processing took {agent_time:.3f}s, should be < 0.03s"
        
        # Print performance results
        stats = benchmark.get_all_stats()
        for operation, stat in stats.items():
            print(f"   {operation}: {stat['average_time']:.3f}s")
        
        print("   ✅ MCP tool performance benchmarks passed")

    def _find_node_by_type(self, nodes: List[Dict[str, Any]], node_type: str) -> Dict[str, Any]:
        """Find node by type"""
        for node in nodes:
            if node["type"] == node_type:
                return node
        return None
