"""
End-to-End Integration Tests for Tool Workflow
Tests the complete tool integration workflow from frontend to backend execution
"""

import pytest
import asyncio
import json
import time
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock, patch

# Import test utilities
from tests.utils.test_helpers import (
    create_test_workflow,
    create_test_component,
    create_test_agent_config,
    MockKafkaClient,
    MockWorkflowService,
    MockOrchestrationEngine,
    MockAgentService
)


class TestCompleteToolWorkflowE2E:
    """End-to-end integration tests for complete tool workflow"""

    @pytest.fixture
    def mock_services(self):
        """Set up mock services for E2E testing"""
        return {
            "workflow_service": MockWorkflowService(),
            "orchestration_engine": MockOrchestrationEngine(),
            "agent_service": MockAgentService(),
            "kafka_client": MockKafkaClient()
        }

    @pytest.mark.asyncio
    async def test_complete_workflow_component_building_to_execution(self, mock_services):
        """
        Test complete workflow: component building → API → frontend → tool connections → execution
        """
        print("\n🧪 Testing Complete Workflow: Component Building → Execution")
        
        # Step 1: Component Building - Create workflow components
        print("   Step 1: Building workflow components...")
        
        # Create a data processor component
        data_processor = create_test_component(
            component_id="data-processor-1",
            component_type="DataProcessor",
            component_name="Data Processing Tool",
            inputs=[{"name": "input_data", "type": "string"}],
            outputs=[{"name": "processed_data", "type": "object"}]
        )
        
        # Create an API caller component
        api_caller = create_test_component(
            component_id="api-caller-1",
            component_type="APICaller",
            component_name="API Calling Tool",
            inputs=[{"name": "url", "type": "string"}, {"name": "method", "type": "string"}],
            outputs=[{"name": "response", "type": "object"}]
        )
        
        # Register components with workflow service
        mock_services["workflow_service"].register_component(data_processor)
        mock_services["workflow_service"].register_component(api_caller)
        
        # Step 2: API Layer - Create workflow with AgenticAI node
        print("   Step 2: Creating workflow via API...")
        
        workflow = create_test_workflow(
            workflow_id="e2e-test-workflow",
            nodes=[
                {
                    "id": "start-node",
                    "type": "StartNode",
                    "position": {"x": 100, "y": 100}
                },
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {
                        "query": "Process the data and call the API",
                        "execution_type": "response",
                        "model_provider": "OpenAI",
                        "model_name": "gpt-4-turbo"
                    }
                },
                {
                    "id": "data-processor-node",
                    "type": "DataProcessor",
                    "position": {"x": 500, "y": 50},
                    "data": {"input_data": "test data"}
                },
                {
                    "id": "api-caller-node",
                    "type": "APICaller",
                    "position": {"x": 500, "y": 150},
                    "data": {"url": "https://api.example.com", "method": "GET"}
                }
            ],
            edges=[
                {
                    "id": "start-to-agent",
                    "source": "start-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "input"
                },
                {
                    "id": "tool-connection-1",
                    "source": "data-processor-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_1"
                },
                {
                    "id": "tool-connection-2",
                    "source": "api-caller-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_2"
                }
            ]
        )
        
        # Step 3: Frontend Processing - Simulate frontend tool connection handling
        print("   Step 3: Processing tool connections in frontend...")
        
        # Simulate frontend calculating tool connections
        tool_connections = self._calculate_tool_connections(workflow)
        
        assert len(tool_connections) == 2, "Should have 2 tool connections"
        assert tool_connections[0]["component_type"] == "DataProcessor"
        assert tool_connections[1]["component_type"] == "APICaller"
        
        # Step 4: Orchestration Engine - Extract tool schemas
        print("   Step 4: Extracting tool schemas in orchestration engine...")
        
        # Process workflow in orchestration engine
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Verify tool extraction
        agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")
        assert "tools" in agentic_node["data"]
        assert len(agentic_node["data"]["tools"]) == 2
        
        # Verify tool schemas are properly generated
        tools = agentic_node["data"]["tools"]
        data_processor_tool = next(t for t in tools if t["component"]["component_type"] == "DataProcessor")
        api_caller_tool = next(t for t in tools if t["component"]["component_type"] == "APICaller")
        
        assert data_processor_tool["tool_type"] == "workflow_component"
        assert "component_schema" in data_processor_tool["component"]
        assert api_caller_tool["tool_type"] == "workflow_component"
        assert "component_schema" in api_caller_tool["component"]
        
        # Step 5: Agent Service - Create agent with tools
        print("   Step 5: Creating agent with tools in agent service...")
        
        # Create agent config with tools
        agent_config = create_test_agent_config(
            agent_id="e2e-test-agent",
            tools=tools
        )
        
        # Process agent config in agent service
        agent_result = await mock_services["agent_service"].process_agent_config(agent_config)
        
        assert agent_result["status"] == "success"
        assert len(agent_result["tool_functions"]) == 2
        
        # Step 6: Execution - Execute workflow with tool calls
        print("   Step 6: Executing workflow with tool calls...")
        
        # Simulate workflow execution
        execution_result = await mock_services["orchestration_engine"].execute_workflow(
            workflow_id="e2e-test-workflow",
            execution_id="e2e-test-execution"
        )
        
        assert execution_result["status"] == "success"
        assert "tool_executions" in execution_result
        assert len(execution_result["tool_executions"]) == 2
        
        print("   ✅ Complete workflow test passed!")

    @pytest.mark.asyncio
    async def test_multiple_tool_connections_single_node(self, mock_services):
        """Test multiple tool connections on single AgenticAI node"""
        print("\n🧪 Testing Multiple Tool Connections on Single Node")
        
        # Create workflow with 5 different tool components
        components = []
        for i in range(5):
            component = create_test_component(
                component_id=f"tool-component-{i+1}",
                component_type=f"ToolComponent{i+1}",
                component_name=f"Tool Component {i+1}",
                inputs=[{"name": "input", "type": "string"}],
                outputs=[{"name": "output", "type": "object"}]
            )
            components.append(component)
            mock_services["workflow_service"].register_component(component)
        
        # Create workflow with all components connected as tools
        workflow_nodes = [
            {
                "id": "agentic-ai-node",
                "type": "AgenticAI",
                "position": {"x": 300, "y": 100},
                "data": {"query": "Use all available tools"}
            }
        ]
        
        workflow_edges = []
        
        # Add component nodes and tool connections
        for i, component in enumerate(components):
            workflow_nodes.append({
                "id": f"tool-node-{i+1}",
                "type": component["component_type"],
                "position": {"x": 500, "y": 50 + i * 50},
                "data": {"input": f"test data {i+1}"}
            })
            
            workflow_edges.append({
                "id": f"tool-connection-{i+1}",
                "source": f"tool-node-{i+1}",
                "target": "agentic-ai-node",
                "sourceHandle": "output",
                "targetHandle": f"tool_{i+1}"
            })
        
        workflow = create_test_workflow(
            workflow_id="multi-tool-test",
            nodes=workflow_nodes,
            edges=workflow_edges
        )
        
        # Process workflow
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Verify all tools are extracted
        agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")
        assert len(agentic_node["data"]["tools"]) == 5
        
        # Verify each tool has proper schema
        for i, tool in enumerate(agentic_node["data"]["tools"]):
            assert tool["component"]["component_type"] == f"ToolComponent{i+1}"
            assert "component_schema" in tool["component"]
        
        print("   ✅ Multiple tool connections test passed!")

    @pytest.mark.asyncio
    async def test_mixed_regular_and_mcp_component_tools(self, mock_services):
        """Test mixed regular and MCP component tools"""
        print("\n🧪 Testing Mixed Regular and MCP Component Tools")
        
        # Create regular workflow component
        regular_component = create_test_component(
            component_id="regular-tool-1",
            component_type="RegularTool",
            component_name="Regular Tool",
            inputs=[{"name": "input", "type": "string"}],
            outputs=[{"name": "output", "type": "object"}]
        )
        
        # Create MCP component
        mcp_component = create_test_component(
            component_id="mcp-tool-1",
            component_type="MCPMarketplace",
            component_name="MCP Tool",
            inputs=[{"name": "input", "type": "string"}],
            outputs=[{"name": "output", "type": "object"}],
            mcp_metadata={
                "server_url": "http://localhost:8000",
                "tool_name": "mcp_test_tool"
            }
        )
        
        # Register components
        mock_services["workflow_service"].register_component(regular_component)
        mock_services["workflow_service"].register_component(mcp_component)
        
        # Create workflow with both types
        workflow = create_test_workflow(
            workflow_id="mixed-tools-test",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Use both regular and MCP tools"}
                },
                {
                    "id": "regular-tool-node",
                    "type": "RegularTool",
                    "position": {"x": 500, "y": 50},
                    "data": {"input": "regular data"}
                },
                {
                    "id": "mcp-tool-node",
                    "type": "MCPMarketplace",
                    "position": {"x": 500, "y": 150},
                    "data": {"input": "mcp data"}
                }
            ],
            edges=[
                {
                    "id": "regular-tool-connection",
                    "source": "regular-tool-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_1"
                },
                {
                    "id": "mcp-tool-connection",
                    "source": "mcp-tool-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_2"
                }
            ]
        )
        
        # Process workflow
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Verify both tool types are extracted
        agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")
        tools = agentic_node["data"]["tools"]
        
        assert len(tools) == 2
        
        # Find regular and MCP tools
        regular_tool = next(t for t in tools if t["component"]["component_type"] == "RegularTool")
        mcp_tool = next(t for t in tools if t["component"]["component_type"] == "MCPMarketplace")
        
        # Verify regular tool
        assert regular_tool["tool_type"] == "workflow_component"
        assert "component_schema" in regular_tool["component"]
        
        # Verify MCP tool
        assert mcp_tool["tool_type"] == "mcp_marketplace"
        assert "mcp_metadata" in mcp_tool["component"]
        assert mcp_tool["component"]["mcp_metadata"]["server_url"] == "http://localhost:8000"
        
        print("   ✅ Mixed regular and MCP tools test passed!")

    def _calculate_tool_connections(self, workflow: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Simulate frontend tool connection calculation"""
        tool_connections = []
        
        # Find AgenticAI node
        agentic_node = self._find_node_by_type(workflow["nodes"], "AgenticAI")
        if not agentic_node:
            return tool_connections
        
        # Find edges connecting to tool handles
        for edge in workflow["edges"]:
            if (edge["target"] == agentic_node["id"] and 
                edge["targetHandle"] and 
                edge["targetHandle"].startswith("tool_")):
                
                # Find source node
                source_node = self._find_node_by_id(workflow["nodes"], edge["source"])
                if source_node:
                    tool_connections.append({
                        "node_id": source_node["id"],
                        "component_type": source_node["type"],
                        "handle_id": edge["targetHandle"]
                    })
        
        return tool_connections
    
    def _find_node_by_type(self, nodes: List[Dict[str, Any]], node_type: str) -> Dict[str, Any]:
        """Find node by type"""
        for node in nodes:
            if node["type"] == node_type:
                return node
        return None
    
    def _find_node_by_id(self, nodes: List[Dict[str, Any]], node_id: str) -> Dict[str, Any]:
        """Find node by ID"""
        for node in nodes:
            if node["id"] == node_id:
                return node
        return None

    @pytest.mark.asyncio
    async def test_tool_disconnection_and_reconnection(self, mock_services):
        """Test tool disconnection and reconnection scenarios"""
        print("\n🧪 Testing Tool Disconnection and Reconnection")

        # Create initial workflow with tool connection
        component = create_test_component(
            component_id="dynamic-tool-1",
            component_type="DynamicTool",
            component_name="Dynamic Tool",
            inputs=[{"name": "input", "type": "string"}],
            outputs=[{"name": "output", "type": "object"}]
        )

        mock_services["workflow_service"].register_component(component)

        # Initial workflow with tool connection
        workflow = create_test_workflow(
            workflow_id="dynamic-tool-test",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Use the dynamic tool"}
                },
                {
                    "id": "dynamic-tool-node",
                    "type": "DynamicTool",
                    "position": {"x": 500, "y": 100},
                    "data": {"input": "test data"}
                }
            ],
            edges=[
                {
                    "id": "tool-connection",
                    "source": "dynamic-tool-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_1"
                }
            ]
        )

        # Process initial workflow
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")

        assert len(agentic_node["data"]["tools"]) == 1
        print("   ✅ Initial tool connection established")

        # Simulate tool disconnection (remove edge)
        workflow["edges"] = []

        # Process workflow after disconnection
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")

        assert len(agentic_node["data"]["tools"]) == 0
        print("   ✅ Tool disconnection handled correctly")

        # Simulate tool reconnection (add edge back)
        workflow["edges"] = [
            {
                "id": "tool-reconnection",
                "source": "dynamic-tool-node",
                "target": "agentic-ai-node",
                "sourceHandle": "output",
                "targetHandle": "tool_1"
            }
        ]

        # Process workflow after reconnection
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")

        assert len(agentic_node["data"]["tools"]) == 1
        print("   ✅ Tool reconnection handled correctly")

    @pytest.mark.asyncio
    async def test_error_scenarios_and_recovery(self, mock_services):
        """Test error scenarios and recovery mechanisms"""
        print("\n🧪 Testing Error Scenarios and Recovery")

        # Test 1: Invalid component schema
        print("   Testing invalid component schema...")

        invalid_component = {
            "component_id": "invalid-tool-1",
            "component_type": "InvalidTool",
            "component_name": "Invalid Tool",
            # Missing required fields
        }

        mock_services["workflow_service"].register_component(invalid_component)

        workflow = create_test_workflow(
            workflow_id="error-test-1",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Use invalid tool"}
                },
                {
                    "id": "invalid-tool-node",
                    "type": "InvalidTool",
                    "position": {"x": 500, "y": 100},
                    "data": {}
                }
            ],
            edges=[
                {
                    "id": "invalid-tool-connection",
                    "source": "invalid-tool-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_1"
                }
            ]
        )

        # Process workflow with invalid component
        try:
            processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
            # Should handle gracefully and exclude invalid tools
            agentic_node = self._find_node_by_type(processed_workflow["nodes"], "AgenticAI")
            # Invalid tools should be filtered out
            assert len(agentic_node["data"]["tools"]) == 0
            print("   ✅ Invalid component schema handled gracefully")
        except Exception as e:
            print(f"   ✅ Invalid component error caught: {str(e)}")

        # Test 2: Network timeout simulation
        print("   Testing network timeout scenarios...")

        # Simulate network timeout in agent service
        original_process = mock_services["agent_service"].process_agent_config

        async def timeout_process(*args, **kwargs):
            await asyncio.sleep(2)  # Simulate timeout
            raise asyncio.TimeoutError("Network timeout")

        mock_services["agent_service"].process_agent_config = timeout_process

        agent_config = create_test_agent_config("timeout-test-agent")

        try:
            await asyncio.wait_for(
                mock_services["agent_service"].process_agent_config(agent_config),
                timeout=1.0
            )
        except asyncio.TimeoutError:
            print("   ✅ Network timeout handled correctly")

        # Restore original method
        mock_services["agent_service"].process_agent_config = original_process

        # Test 3: Kafka message failure
        print("   Testing Kafka message failure...")

        # Simulate Kafka failure
        original_send = mock_services["kafka_client"].send_message

        async def failing_send(*args, **kwargs):
            raise ConnectionError("Kafka connection failed")

        mock_services["kafka_client"].send_message = failing_send

        try:
            await mock_services["kafka_client"].send_message("test-topic", {"test": "data"})
        except ConnectionError:
            print("   ✅ Kafka failure handled correctly")

        # Restore original method
        mock_services["kafka_client"].send_message = original_send

        print("   ✅ All error scenarios tested successfully!")

    @pytest.mark.asyncio
    async def test_performance_benchmarks(self, mock_services):
        """Test performance benchmarks for tool workflow"""
        print("\n🧪 Testing Performance Benchmarks")

        from tests.utils.test_helpers import PerformanceBenchmark

        benchmark = PerformanceBenchmark()

        # Create test workflow
        component = create_test_component(
            component_id="perf-tool-1",
            component_type="PerformanceTool",
            component_name="Performance Tool",
            inputs=[{"name": "input", "type": "string"}],
            outputs=[{"name": "output", "type": "object"}]
        )

        mock_services["workflow_service"].register_component(component)

        workflow = create_test_workflow(
            workflow_id="performance-test",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Performance test"}
                },
                {
                    "id": "perf-tool-node",
                    "type": "PerformanceTool",
                    "position": {"x": 500, "y": 100},
                    "data": {"input": "performance data"}
                }
            ],
            edges=[
                {
                    "id": "perf-tool-connection",
                    "source": "perf-tool-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_1"
                }
            ]
        )

        # Benchmark workflow processing
        _, processing_time = await benchmark.benchmark_operation(
            "workflow_processing",
            mock_services["orchestration_engine"].process_workflow,
            workflow
        )

        # Benchmark agent config processing
        agent_config = create_test_agent_config("perf-test-agent", [])
        _, agent_time = await benchmark.benchmark_operation(
            "agent_processing",
            mock_services["agent_service"].process_agent_config,
            agent_config
        )

        # Benchmark workflow execution
        _, execution_time = await benchmark.benchmark_operation(
            "workflow_execution",
            mock_services["orchestration_engine"].execute_workflow,
            "performance-test",
            "perf-execution"
        )

        # Verify performance targets
        assert processing_time < 0.1, f"Workflow processing took {processing_time:.3f}s, should be < 0.1s"
        assert agent_time < 0.05, f"Agent processing took {agent_time:.3f}s, should be < 0.05s"
        assert execution_time < 0.2, f"Workflow execution took {execution_time:.3f}s, should be < 0.2s"

        # Print benchmark results
        stats = benchmark.get_all_stats()
        for operation, stat in stats.items():
            print(f"   {operation}: {stat['average_time']:.3f}s (min: {stat['min_time']:.3f}s, max: {stat['max_time']:.3f}s)")

        print("   ✅ All performance benchmarks passed!")
