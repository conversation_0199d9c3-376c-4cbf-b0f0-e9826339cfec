"""
Simple E2E validation test to verify the test infrastructure works
"""

import pytest
import asyncio
from tests.utils.test_helpers import (
    create_test_workflow,
    create_test_component,
    MockWorkflowService,
    MockOrchestrationEngine,
    MockAgentService
)


class TestE2EValidation:
    """Simple validation tests for E2E infrastructure"""

    @pytest.fixture
    def mock_services(self):
        """Set up mock services"""
        return {
            "workflow_service": MockWorkflowService(),
            "orchestration_engine": MockOrchestrationEngine(),
            "agent_service": MockAgentService()
        }

    def test_test_helpers_import(self):
        """Test that test helpers can be imported"""
        assert create_test_workflow is not None
        assert create_test_component is not None
        assert MockWorkflowService is not None
        print("✅ Test helpers imported successfully")

    def test_create_test_component(self):
        """Test component creation utility"""
        component = create_test_component(
            component_id="test-component-1",
            component_type="TestComponent",
            component_name="Test Component",
            inputs=[{"name": "input", "type": "string"}],
            outputs=[{"name": "output", "type": "object"}]
        )
        
        assert component["component_id"] == "test-component-1"
        assert component["component_type"] == "TestComponent"
        assert component["component_name"] == "Test Component"
        assert len(component["inputs"]) == 1
        assert len(component["outputs"]) == 1
        assert "component_schema" in component
        print("✅ Component creation utility works")

    def test_create_test_workflow(self):
        """Test workflow creation utility"""
        workflow = create_test_workflow(
            workflow_id="test-workflow-1",
            nodes=[
                {
                    "id": "node-1",
                    "type": "TestNode",
                    "position": {"x": 100, "y": 100}
                }
            ],
            edges=[
                {
                    "id": "edge-1",
                    "source": "node-1",
                    "target": "node-2"
                }
            ]
        )
        
        assert workflow["id"] == "test-workflow-1"
        assert len(workflow["nodes"]) == 1
        assert len(workflow["edges"]) == 1
        assert "metadata" in workflow
        print("✅ Workflow creation utility works")

    @pytest.mark.asyncio
    async def test_mock_services(self, mock_services):
        """Test mock services functionality"""
        # Test workflow service
        component = create_test_component(
            component_id="mock-test-1",
            component_type="MockTest",
            component_name="Mock Test Component"
        )
        
        mock_services["workflow_service"].register_component(component)
        retrieved = mock_services["workflow_service"].get_component("mock-test-1")
        
        assert retrieved is not None
        assert retrieved["component_id"] == "mock-test-1"
        print("✅ Mock workflow service works")
        
        # Test orchestration engine
        workflow = create_test_workflow(
            workflow_id="mock-workflow-1",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Test query"}
                }
            ],
            edges=[]
        )
        
        processed = await mock_services["orchestration_engine"].process_workflow(workflow)
        assert processed is not None
        assert processed["id"] == "mock-workflow-1"
        print("✅ Mock orchestration engine works")
        
        # Test agent service
        agent_config = {
            "id": "test-agent-1",
            "name": "Test Agent",
            "tools": []
        }
        
        result = await mock_services["agent_service"].process_agent_config(agent_config)
        assert result["status"] == "success"
        assert result["agent_id"] == "test-agent-1"
        print("✅ Mock agent service works")

    @pytest.mark.asyncio
    async def test_simple_tool_workflow(self, mock_services):
        """Test a simple tool workflow end-to-end"""
        print("\n🧪 Testing Simple Tool Workflow")
        
        # Create and register a component
        component = create_test_component(
            component_id="simple-tool-1",
            component_type="SimpleTool",
            component_name="Simple Tool",
            inputs=[{"name": "input", "type": "string"}],
            outputs=[{"name": "output", "type": "object"}]
        )
        
        mock_services["workflow_service"].register_component(component)
        
        # Create workflow with tool connection
        workflow = create_test_workflow(
            workflow_id="simple-tool-workflow",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Use the simple tool"}
                },
                {
                    "id": "simple-tool-node",
                    "type": "SimpleTool",
                    "position": {"x": 500, "y": 100},
                    "data": {"input": "test data"}
                }
            ],
            edges=[
                {
                    "id": "tool-connection",
                    "source": "simple-tool-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_1"
                }
            ]
        )
        
        # Process workflow
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Find AgenticAI node and verify tools
        agentic_node = None
        for node in processed_workflow["nodes"]:
            if node["type"] == "AgenticAI":
                agentic_node = node
                break
        
        assert agentic_node is not None
        assert "tools" in agentic_node["data"]
        assert len(agentic_node["data"]["tools"]) == 1
        
        tool = agentic_node["data"]["tools"][0]
        assert tool["tool_type"] == "workflow_component"
        assert tool["component"]["component_type"] == "SimpleTool"
        
        print("   ✅ Simple tool workflow completed successfully")
        print("   ✅ Tool extraction working correctly")
        print("   ✅ Mock services integration successful")

    def test_performance_benchmark_utility(self):
        """Test performance benchmark utility"""
        from tests.utils.test_helpers import PerformanceBenchmark
        
        benchmark = PerformanceBenchmark()
        assert benchmark is not None
        
        # Test benchmark stats
        stats = benchmark.get_all_stats()
        assert isinstance(stats, dict)
        
        print("✅ Performance benchmark utility works")

    def test_e2e_infrastructure_ready(self):
        """Verify E2E test infrastructure is ready"""
        print("\n🎯 E2E Test Infrastructure Status:")
        print("   ✅ Test helpers imported and functional")
        print("   ✅ Mock services created and working")
        print("   ✅ Component creation utilities ready")
        print("   ✅ Workflow creation utilities ready")
        print("   ✅ Performance benchmarking ready")
        print("   ✅ Async test support enabled")
        print("\n🚀 E2E Test Infrastructure is READY!")
        
        assert True  # Infrastructure validation passed
