"""
Performance Benchmark Tests for Tool Integration
Tests performance targets and benchmarks for the complete tool integration system
"""

import pytest
import asyncio
import time
import statistics
from typing import Dict, Any, List
from unittest.mock import Mock, AsyncMock

# Import test utilities
from tests.utils.test_helpers import (
    create_test_workflow,
    create_test_component,
    create_test_agent_config,
    MockWorkflowService,
    MockOrchestrationEngine,
    MockAgentService,
    PerformanceBenchmark
)


class TestToolPerformanceBenchmarks:
    """Performance benchmark tests for tool integration"""

    @pytest.fixture
    def mock_services(self):
        """Set up mock services for performance testing"""
        return {
            "workflow_service": MockWorkflowService(),
            "orchestration_engine": MockOrchestrationEngine(),
            "agent_service": MockAgentService()
        }

    @pytest.fixture
    def performance_components(self):
        """Create components for performance testing"""
        components = []
        for i in range(10):
            component = create_test_component(
                component_id=f"perf-component-{i+1}",
                component_type=f"PerfComponent{i+1}",
                component_name=f"Performance Component {i+1}",
                inputs=[{"name": "input", "type": "string"}],
                outputs=[{"name": "output", "type": "object"}]
            )
            components.append(component)
        return components

    @pytest.mark.asyncio
    async def test_ui_interaction_response_time(self, mock_services):
        """Test UI interactions: <100ms response time"""
        print("\n⚡ Testing UI Interaction Response Time (<100ms)")
        
        benchmark = PerformanceBenchmark()
        
        # Simulate UI operations
        ui_operations = [
            ("component_discovery", self._simulate_component_discovery),
            ("tool_connection_calculation", self._simulate_tool_connection_calculation),
            ("inspector_panel_update", self._simulate_inspector_panel_update),
            ("workflow_validation", self._simulate_workflow_validation)
        ]
        
        for operation_name, operation_func in ui_operations:
            # Run operation multiple times for statistical accuracy
            times = []
            for _ in range(10):
                _, execution_time = await benchmark.benchmark_operation(
                    operation_name,
                    operation_func,
                    mock_services
                )
                times.append(execution_time * 1000)  # Convert to milliseconds
            
            avg_time = statistics.mean(times)
            max_time = max(times)
            
            # Verify performance target
            assert avg_time < 100, f"{operation_name} average time {avg_time:.1f}ms > 100ms"
            assert max_time < 150, f"{operation_name} max time {max_time:.1f}ms > 150ms"
            
            print(f"   ✅ {operation_name}: {avg_time:.1f}ms avg, {max_time:.1f}ms max")
        
        print("   ✅ All UI interaction benchmarks passed!")

    @pytest.mark.asyncio
    async def test_schema_generation_performance(self, mock_services, performance_components):
        """Test schema generation: <50ms per component"""
        print("\n⚡ Testing Schema Generation Performance (<50ms per component)")
        
        benchmark = PerformanceBenchmark()
        
        # Register components
        for component in performance_components:
            mock_services["workflow_service"].register_component(component)
        
        # Test single component schema generation
        single_times = []
        for component in performance_components[:5]:  # Test 5 components
            start_time = time.time()
            
            # Simulate schema generation
            await self._simulate_schema_generation(component)
            
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
            single_times.append(execution_time)
        
        avg_single_time = statistics.mean(single_times)
        max_single_time = max(single_times)
        
        assert avg_single_time < 50, f"Schema generation average {avg_single_time:.1f}ms > 50ms"
        assert max_single_time < 75, f"Schema generation max {max_single_time:.1f}ms > 75ms"
        
        print(f"   ✅ Single component schema generation: {avg_single_time:.1f}ms avg, {max_single_time:.1f}ms max")
        
        # Test batch schema generation
        start_time = time.time()
        
        # Simulate batch schema generation for all components
        await self._simulate_batch_schema_generation(performance_components)
        
        end_time = time.time()
        batch_time = (end_time - start_time) * 1000  # Convert to milliseconds
        per_component_time = batch_time / len(performance_components)
        
        assert per_component_time < 30, f"Batch schema generation {per_component_time:.1f}ms per component > 30ms"
        
        print(f"   ✅ Batch schema generation: {per_component_time:.1f}ms per component")
        print("   ✅ Schema generation benchmarks passed!")

    @pytest.mark.asyncio
    async def test_tool_extraction_performance(self, mock_services, performance_components):
        """Test tool extraction: <100ms for 10 tools"""
        print("\n⚡ Testing Tool Extraction Performance (<100ms for 10 tools)")
        
        benchmark = PerformanceBenchmark()
        
        # Register components
        for component in performance_components:
            mock_services["workflow_service"].register_component(component)
        
        # Create workflow with 10 tool connections
        workflow_nodes = [
            {
                "id": "agentic-ai-node",
                "type": "AgenticAI",
                "position": {"x": 300, "y": 100},
                "data": {"query": "Use all 10 tools"}
            }
        ]
        
        workflow_edges = []
        
        # Add 10 tool nodes and connections
        for i in range(10):
            workflow_nodes.append({
                "id": f"tool-node-{i+1}",
                "type": f"PerfComponent{i+1}",
                "position": {"x": 500, "y": 50 + i * 30},
                "data": {"input": f"test data {i+1}"}
            })
            
            workflow_edges.append({
                "id": f"tool-connection-{i+1}",
                "source": f"tool-node-{i+1}",
                "target": "agentic-ai-node",
                "sourceHandle": "output",
                "targetHandle": f"tool_{i+1}"
            })
        
        workflow = create_test_workflow(
            workflow_id="tool-extraction-perf-test",
            nodes=workflow_nodes,
            edges=workflow_edges
        )
        
        # Benchmark tool extraction
        extraction_times = []
        for _ in range(5):  # Run 5 times for statistical accuracy
            _, execution_time = await benchmark.benchmark_operation(
                "tool_extraction_10_tools",
                mock_services["orchestration_engine"].process_workflow,
                workflow
            )
            extraction_times.append(execution_time * 1000)  # Convert to milliseconds
        
        avg_extraction_time = statistics.mean(extraction_times)
        max_extraction_time = max(extraction_times)
        
        assert avg_extraction_time < 100, f"Tool extraction average {avg_extraction_time:.1f}ms > 100ms"
        assert max_extraction_time < 150, f"Tool extraction max {max_extraction_time:.1f}ms > 150ms"
        
        print(f"   ✅ 10 tools extraction: {avg_extraction_time:.1f}ms avg, {max_extraction_time:.1f}ms max")
        
        # Test scalability with different tool counts
        tool_counts = [1, 3, 5, 10]
        for tool_count in tool_counts:
            # Create workflow with specific tool count
            limited_workflow = create_test_workflow(
                workflow_id=f"tool-extraction-{tool_count}-tools",
                nodes=workflow_nodes[:tool_count+1],  # +1 for AgenticAI node
                edges=workflow_edges[:tool_count]
            )
            
            _, execution_time = await benchmark.benchmark_operation(
                f"tool_extraction_{tool_count}_tools",
                mock_services["orchestration_engine"].process_workflow,
                limited_workflow
            )
            
            per_tool_time = (execution_time * 1000) / tool_count
            print(f"   ✅ {tool_count} tools: {execution_time*1000:.1f}ms total, {per_tool_time:.1f}ms per tool")
        
        print("   ✅ Tool extraction benchmarks passed!")

    @pytest.mark.asyncio
    async def test_memory_usage_performance(self, mock_services, performance_components):
        """Test memory usage: <50MB increase for 10 connected tools"""
        print("\n⚡ Testing Memory Usage Performance (<50MB for 10 tools)")
        
        import psutil
        import os
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # Convert to MB
        
        # Register components
        for component in performance_components:
            mock_services["workflow_service"].register_component(component)
        
        # Create workflow with 10 tool connections
        workflow = create_test_workflow(
            workflow_id="memory-usage-test",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Memory usage test"}
                }
            ] + [
                {
                    "id": f"tool-node-{i+1}",
                    "type": f"PerfComponent{i+1}",
                    "position": {"x": 500, "y": 50 + i * 30},
                    "data": {"input": f"test data {i+1}"}
                }
                for i in range(10)
            ],
            edges=[
                {
                    "id": f"tool-connection-{i+1}",
                    "source": f"tool-node-{i+1}",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": f"tool_{i+1}"
                }
                for i in range(10)
            ]
        )
        
        # Process workflow and create agent config
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Get memory after workflow processing
        workflow_memory = process.memory_info().rss / 1024 / 1024
        workflow_increase = workflow_memory - initial_memory
        
        # Extract tools and create agent config
        agentic_node = None
        for node in processed_workflow["nodes"]:
            if node["type"] == "AgenticAI":
                agentic_node = node
                break
        
        tools = agentic_node["data"]["tools"] if agentic_node else []
        agent_config = create_test_agent_config("memory-test-agent", tools)
        
        # Process agent config
        await mock_services["agent_service"].process_agent_config(agent_config)
        
        # Get final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024
        total_increase = final_memory - initial_memory
        
        print(f"   Initial memory: {initial_memory:.1f}MB")
        print(f"   After workflow processing: {workflow_memory:.1f}MB (+{workflow_increase:.1f}MB)")
        print(f"   Final memory: {final_memory:.1f}MB (+{total_increase:.1f}MB total)")
        
        # Verify memory usage target
        assert total_increase < 50, f"Memory increase {total_increase:.1f}MB > 50MB"
        
        print("   ✅ Memory usage benchmark passed!")

    @pytest.mark.asyncio
    async def test_end_to_end_performance(self, mock_services, performance_components):
        """Test complete end-to-end performance"""
        print("\n⚡ Testing End-to-End Performance")
        
        benchmark = PerformanceBenchmark()
        
        # Register components
        for component in performance_components[:5]:  # Use 5 components for E2E test
            mock_services["workflow_service"].register_component(component)
        
        # Create complete workflow
        workflow = create_test_workflow(
            workflow_id="e2e-performance-test",
            nodes=[
                {
                    "id": "start-node",
                    "type": "StartNode",
                    "position": {"x": 100, "y": 100}
                },
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "E2E performance test"}
                }
            ] + [
                {
                    "id": f"tool-node-{i+1}",
                    "type": f"PerfComponent{i+1}",
                    "position": {"x": 500, "y": 50 + i * 40},
                    "data": {"input": f"test data {i+1}"}
                }
                for i in range(5)
            ],
            edges=[
                {
                    "id": "start-to-agent",
                    "source": "start-node",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "input"
                }
            ] + [
                {
                    "id": f"tool-connection-{i+1}",
                    "source": f"tool-node-{i+1}",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": f"tool_{i+1}"
                }
                for i in range(5)
            ]
        )
        
        # Benchmark complete E2E flow
        start_time = time.time()
        
        # Step 1: Process workflow
        processed_workflow = await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Step 2: Extract tools
        agentic_node = None
        for node in processed_workflow["nodes"]:
            if node["type"] == "AgenticAI":
                agentic_node = node
                break
        
        tools = agentic_node["data"]["tools"] if agentic_node else []
        
        # Step 3: Create and process agent config
        agent_config = create_test_agent_config("e2e-perf-agent", tools)
        await mock_services["agent_service"].process_agent_config(agent_config)
        
        # Step 4: Execute workflow
        await mock_services["orchestration_engine"].execute_workflow(
            "e2e-performance-test",
            "e2e-perf-execution"
        )
        
        end_time = time.time()
        total_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        print(f"   Complete E2E flow: {total_time:.1f}ms")
        print(f"   Tools processed: {len(tools)}")
        print(f"   Time per tool: {total_time/len(tools):.1f}ms")
        
        # Verify E2E performance target (should be reasonable for 5 tools)
        assert total_time < 500, f"E2E performance {total_time:.1f}ms > 500ms"
        
        print("   ✅ End-to-end performance benchmark passed!")

    async def _simulate_component_discovery(self, mock_services):
        """Simulate component discovery operation"""
        await asyncio.sleep(0.01)  # Simulate API call
        return mock_services["workflow_service"].get_all_components()

    async def _simulate_tool_connection_calculation(self, mock_services):
        """Simulate tool connection calculation"""
        await asyncio.sleep(0.005)  # Simulate frontend calculation
        return {"tool_connections": 3, "calculation_time": 0.005}

    async def _simulate_inspector_panel_update(self, mock_services):
        """Simulate inspector panel update"""
        await asyncio.sleep(0.008)  # Simulate UI update
        return {"panel_updated": True, "tools_displayed": 5}

    async def _simulate_workflow_validation(self, mock_services):
        """Simulate workflow validation"""
        await asyncio.sleep(0.012)  # Simulate validation logic
        return {"validation_result": "valid", "tool_connections_valid": True}

    async def _simulate_schema_generation(self, component):
        """Simulate schema generation for a component"""
        await asyncio.sleep(0.002)  # Simulate schema processing
        return {
            "component_id": component["component_id"],
            "schema_generated": True,
            "schema_size": len(str(component))
        }

    async def _simulate_batch_schema_generation(self, components):
        """Simulate batch schema generation"""
        await asyncio.sleep(0.001 * len(components))  # Simulate batch processing
        return {
            "components_processed": len(components),
            "schemas_generated": len(components)
        }
