"""
Comprehensive Performance Tests
Tests using advanced performance optimization and benchmarking utilities
"""

import pytest
import asyncio
import time
from typing import Dict, Any, List

# Import performance utilities
from workflow_service.app.utils.performance.performance_optimizer import get_performance_optimizer
from workflow_service.app.utils.performance.advanced_benchmarking import get_advanced_benchmarking

# Import test utilities
from tests.utils.test_helpers import (
    create_test_workflow,
    create_test_component,
    MockWorkflowService,
    MockOrchestrationEngine,
    MockAgentService
)


class TestComprehensivePerformance:
    """Comprehensive performance tests using advanced utilities"""

    @pytest.fixture
    def mock_services(self):
        """Set up mock services for performance testing"""
        return {
            "workflow_service": MockWorkflowService(),
            "orchestration_engine": MockOrchestrationEngine(),
            "agent_service": MockAgentService()
        }

    @pytest.fixture
    def performance_optimizer(self):
        """Get performance optimizer instance"""
        return get_performance_optimizer()

    @pytest.fixture
    def advanced_benchmarking(self):
        """Get advanced benchmarking instance"""
        return get_advanced_benchmarking()

    @pytest.mark.asyncio
    async def test_optimized_schema_generation_performance(
        self, 
        mock_services, 
        performance_optimizer, 
        advanced_benchmarking
    ):
        """Test optimized schema generation performance"""
        print("\n⚡ Testing Optimized Schema Generation Performance")
        
        # Create test components
        components = []
        for i in range(20):
            component = create_test_component(
                component_id=f"perf-component-{i+1}",
                component_type=f"PerfComponent{i+1}",
                component_name=f"Performance Component {i+1}",
                inputs=[
                    {"name": "input1", "type": "string"},
                    {"name": "input2", "type": "number"},
                    {"name": "input3", "type": "object"}
                ],
                outputs=[{"name": "output", "type": "object"}]
            )
            components.append(component)
        
        # Benchmark original schema generation
        async def original_schema_generation():
            schemas = []
            for component in components:
                schema = {
                    "name": component["component_id"].replace("-", "_"),
                    "description": component["component_name"],
                    "parameters": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                }
                
                # Process inputs
                for inp in component.get("inputs", []):
                    schema["parameters"]["properties"][inp["name"]] = {
                        "type": inp["type"],
                        "description": f"Input parameter {inp['name']}"
                    }
                    schema["parameters"]["required"].append(inp["name"])
                
                schemas.append(schema)
                await asyncio.sleep(0.001)  # Simulate processing time
            
            return schemas
        
        # Benchmark optimized schema generation
        async def optimized_schema_generation():
            return await performance_optimizer._optimize_schema_generation(components)
        
        # Run comprehensive benchmarks
        original_metrics = await advanced_benchmarking.comprehensive_benchmark(
            "original_schema_generation",
            original_schema_generation,
            iterations=50
        )
        
        optimized_metrics = await advanced_benchmarking.comprehensive_benchmark(
            "optimized_schema_generation", 
            optimized_schema_generation,
            iterations=50
        )
        
        # Validate performance improvements
        improvement_percentage = (
            (original_metrics.average_time - optimized_metrics.average_time) / 
            original_metrics.average_time
        ) * 100
        
        print(f"   Original: {original_metrics.average_time:.3f}s avg")
        print(f"   Optimized: {optimized_metrics.average_time:.3f}s avg")
        print(f"   Improvement: {improvement_percentage:.1f}%")
        
        # Verify optimization targets
        assert optimized_metrics.average_time < 0.05, f"Optimized schema generation {optimized_metrics.average_time:.3f}s > 0.05s"
        assert improvement_percentage > 10, f"Optimization improvement {improvement_percentage:.1f}% < 10%"
        assert optimized_metrics.success_rate > 0.99, f"Success rate {optimized_metrics.success_rate:.3f} < 0.99"
        
        print("   ✅ Optimized schema generation performance validated")

    @pytest.mark.asyncio
    async def test_optimized_tool_extraction_performance(
        self, 
        mock_services, 
        performance_optimizer, 
        advanced_benchmarking
    ):
        """Test optimized tool extraction performance"""
        print("\n⚡ Testing Optimized Tool Extraction Performance")
        
        # Register components
        components = []
        for i in range(15):
            component = create_test_component(
                component_id=f"tool-component-{i+1}",
                component_type=f"ToolComponent{i+1}",
                component_name=f"Tool Component {i+1}",
                inputs=[{"name": "input", "type": "string"}],
                outputs=[{"name": "output", "type": "object"}]
            )
            components.append(component)
            mock_services["workflow_service"].register_component(component)
        
        # Create workflow with many tool connections
        workflow = create_test_workflow(
            workflow_id="tool-extraction-perf-test",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Use all tools"}
                }
            ] + [
                {
                    "id": f"tool-node-{i+1}",
                    "type": f"ToolComponent{i+1}",
                    "position": {"x": 500, "y": 50 + i * 20},
                    "data": {"input": f"test data {i+1}"}
                }
                for i in range(15)
            ],
            edges=[
                {
                    "id": f"tool-connection-{i+1}",
                    "source": f"tool-node-{i+1}",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": f"tool_{i+1}"
                }
                for i in range(15)
            ]
        )
        
        # Benchmark original tool extraction
        async def original_tool_extraction():
            return await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Benchmark optimized tool extraction
        async def optimized_tool_extraction():
            return await performance_optimizer._optimize_tool_extraction(workflow)
        
        # Run comprehensive benchmarks
        original_metrics = await advanced_benchmarking.comprehensive_benchmark(
            "original_tool_extraction",
            original_tool_extraction,
            iterations=30
        )
        
        optimized_metrics = await advanced_benchmarking.comprehensive_benchmark(
            "optimized_tool_extraction",
            optimized_tool_extraction,
            iterations=30
        )
        
        # Validate performance improvements
        improvement_percentage = (
            (original_metrics.average_time - optimized_metrics.average_time) / 
            original_metrics.average_time
        ) * 100
        
        print(f"   Original: {original_metrics.average_time:.3f}s avg")
        print(f"   Optimized: {optimized_metrics.average_time:.3f}s avg")
        print(f"   Improvement: {improvement_percentage:.1f}%")
        
        # Verify optimization targets
        assert optimized_metrics.average_time < 0.1, f"Optimized tool extraction {optimized_metrics.average_time:.3f}s > 0.1s"
        assert improvement_percentage > 5, f"Optimization improvement {improvement_percentage:.1f}% < 5%"
        assert optimized_metrics.memory_average < 20, f"Memory usage {optimized_metrics.memory_average:.1f}MB > 20MB"
        
        print("   ✅ Optimized tool extraction performance validated")

    @pytest.mark.asyncio
    async def test_load_testing_workflow_processing(
        self, 
        mock_services, 
        advanced_benchmarking
    ):
        """Test load testing for workflow processing"""
        print("\n⚡ Testing Load Testing for Workflow Processing")
        
        # Create test workflow
        workflow = create_test_workflow(
            workflow_id="load-test-workflow",
            nodes=[
                {
                    "id": "agentic-ai-node",
                    "type": "AgenticAI",
                    "position": {"x": 300, "y": 100},
                    "data": {"query": "Load test query"}
                },
                {
                    "id": "tool-node-1",
                    "type": "LoadTestTool",
                    "position": {"x": 500, "y": 100},
                    "data": {"input": "load test data"}
                }
            ],
            edges=[
                {
                    "id": "tool-connection-1",
                    "source": "tool-node-1",
                    "target": "agentic-ai-node",
                    "sourceHandle": "output",
                    "targetHandle": "tool_1"
                }
            ]
        )
        
        # Define load test operation
        async def workflow_processing_operation():
            return await mock_services["orchestration_engine"].process_workflow(workflow)
        
        # Run load test with different concurrent user levels
        user_levels = [1, 5, 10, 20]
        
        for users in user_levels:
            print(f"   Testing with {users} concurrent users...")
            
            load_result = await advanced_benchmarking.load_test(
                "workflow_processing_load",
                workflow_processing_operation,
                concurrent_users=users,
                duration=10.0  # 10 seconds
            )
            
            print(f"     Requests/sec: {load_result.requests_per_second:.1f}")
            print(f"     Error rate: {load_result.error_rate:.2%}")
            print(f"     Avg response: {load_result.average_response_time:.3f}s")
            
            # Validate load test results
            assert load_result.error_rate < 0.05, f"Error rate {load_result.error_rate:.2%} > 5%"
            assert load_result.average_response_time < 0.5, f"Response time {load_result.average_response_time:.3f}s > 0.5s"
            assert load_result.requests_per_second > users * 0.5, f"Throughput {load_result.requests_per_second:.1f} < {users * 0.5}"
        
        print("   ✅ Load testing performance validated")

    @pytest.mark.asyncio
    async def test_performance_target_validation(
        self, 
        mock_services, 
        advanced_benchmarking
    ):
        """Test performance target validation"""
        print("\n⚡ Testing Performance Target Validation")
        
        # Define test operations
        async def ui_interaction_simulation():
            await asyncio.sleep(0.02)  # 20ms - should meet target
            return {"ui_updated": True}
        
        async def schema_generation_simulation():
            await asyncio.sleep(0.03)  # 30ms - should meet target
            return {"schema_generated": True}
        
        async def slow_operation_simulation():
            await asyncio.sleep(0.15)  # 150ms - should fail target
            return {"operation_completed": True}
        
        # Benchmark operations
        ui_metrics = await advanced_benchmarking.comprehensive_benchmark(
            "ui_interaction",
            ui_interaction_simulation,
            iterations=50
        )
        
        schema_metrics = await advanced_benchmarking.comprehensive_benchmark(
            "schema_generation",
            schema_generation_simulation,
            iterations=50
        )
        
        slow_metrics = await advanced_benchmarking.comprehensive_benchmark(
            "slow_operation",
            slow_operation_simulation,
            iterations=20
        )
        
        # Validate against performance targets
        ui_validation = advanced_benchmarking.validate_performance_targets(ui_metrics)
        schema_validation = advanced_benchmarking.validate_performance_targets(schema_metrics)
        slow_validation = advanced_benchmarking.validate_performance_targets(slow_metrics)
        
        print(f"   UI Interaction: {ui_validation['summary']}")
        print(f"   Schema Generation: {schema_validation['summary']}")
        print(f"   Slow Operation: {slow_validation.get('summary', 'No target defined')}")
        
        # Verify target validation
        assert ui_validation["target_met"] == True, "UI interaction should meet performance target"
        assert schema_validation["target_met"] == True, "Schema generation should meet performance target"
        
        print("   ✅ Performance target validation working correctly")

    @pytest.mark.asyncio
    async def test_performance_regression_detection(
        self, 
        mock_services, 
        advanced_benchmarking
    ):
        """Test performance regression detection"""
        print("\n⚡ Testing Performance Regression Detection")
        
        # Define baseline operation
        async def baseline_operation():
            await asyncio.sleep(0.02)  # 20ms baseline
            return {"baseline": True}
        
        # Define regressed operation
        async def regressed_operation():
            await asyncio.sleep(0.035)  # 35ms - 75% slower
            return {"regressed": True}
        
        # Establish baseline
        baseline_metrics = await advanced_benchmarking.comprehensive_benchmark(
            "regression_test_operation",
            baseline_operation,
            iterations=30
        )
        
        print(f"   Baseline established: {baseline_metrics.average_time:.3f}s avg")
        
        # Test with regressed performance
        regressed_metrics = await advanced_benchmarking.comprehensive_benchmark(
            "regression_test_operation",
            regressed_operation,
            iterations=30
        )
        
        print(f"   Regressed performance: {regressed_metrics.average_time:.3f}s avg")
        
        # Detect regression
        regression_analysis = advanced_benchmarking.detect_performance_regression(
            "regression_test_operation",
            regressed_metrics,
            regression_threshold=0.1  # 10% threshold
        )
        
        print(f"   Regression detected: {regression_analysis['has_regression']}")
        print(f"   Time change: {regression_analysis['time_change']:.1%}")
        
        # Verify regression detection
        assert regression_analysis["has_regression"] == True, "Should detect performance regression"
        assert regression_analysis["time_change"] > 0.1, "Should detect significant time increase"
        assert regression_analysis["regressions"]["execution_time"] == True, "Should flag execution time regression"
        
        print("   ✅ Performance regression detection working correctly")

    @pytest.mark.asyncio
    async def test_comprehensive_performance_report(
        self, 
        mock_services, 
        advanced_benchmarking
    ):
        """Test comprehensive performance report generation"""
        print("\n⚡ Testing Comprehensive Performance Report")
        
        # Run multiple benchmarks to populate data
        operations = [
            ("ui_interaction", lambda: asyncio.sleep(0.02)),
            ("schema_generation", lambda: asyncio.sleep(0.03)),
            ("tool_extraction", lambda: asyncio.sleep(0.08))
        ]
        
        for op_name, op_func in operations:
            await advanced_benchmarking.comprehensive_benchmark(
                op_name,
                op_func,
                iterations=20
            )
        
        # Generate comprehensive report
        report = advanced_benchmarking.generate_performance_report()
        
        print(f"   Total operations: {report['summary']['total_operations']}")
        print(f"   Total benchmarks: {report['summary']['total_benchmarks']}")
        print(f"   Targets met: {report['summary']['performance_targets_met']}/{report['summary']['performance_targets_total']}")
        
        # Verify report structure
        assert "generated_at" in report
        assert "operations" in report
        assert "summary" in report
        assert report["summary"]["total_operations"] == 3
        assert report["summary"]["total_benchmarks"] >= 3
        
        # Verify operation details
        for op_name, _ in operations:
            assert op_name in report["operations"]
            op_data = report["operations"][op_name]
            assert "latest_metrics" in op_data
            assert "target_validation" in op_data
            assert "benchmark_count" in op_data
        
        print("   ✅ Performance report generation working correctly")
        
        # Export benchmark data
        export_filename = "test_benchmark_export.json"
        advanced_benchmarking.export_benchmark_data(export_filename)
        print(f"   ✅ Benchmark data exported to {export_filename}")

    @pytest.mark.asyncio
    async def test_memory_optimization_validation(
        self, 
        mock_services, 
        performance_optimizer, 
        advanced_benchmarking
    ):
        """Test memory optimization validation"""
        print("\n⚡ Testing Memory Optimization Validation")
        
        # Define memory-intensive operation
        async def memory_intensive_operation():
            # Create large objects
            large_data = [{"data": f"item_{i}" * 100} for i in range(1000)]
            await asyncio.sleep(0.01)
            return len(large_data)
        
        # Define memory-optimized operation
        async def memory_optimized_operation():
            return await performance_optimizer._optimize_memory_usage(memory_intensive_operation)
        
        # Benchmark both operations
        intensive_metrics = await advanced_benchmarking.comprehensive_benchmark(
            "memory_intensive_operation",
            memory_intensive_operation,
            iterations=20
        )
        
        optimized_metrics = await advanced_benchmarking.comprehensive_benchmark(
            "memory_optimized_operation", 
            memory_optimized_operation,
            iterations=20
        )
        
        # Calculate memory improvement
        memory_improvement = intensive_metrics.memory_average - optimized_metrics.memory_average
        memory_improvement_percentage = (memory_improvement / intensive_metrics.memory_average) * 100 if intensive_metrics.memory_average > 0 else 0
        
        print(f"   Original memory: {intensive_metrics.memory_average:.1f}MB avg")
        print(f"   Optimized memory: {optimized_metrics.memory_average:.1f}MB avg")
        print(f"   Memory saved: {memory_improvement:.1f}MB ({memory_improvement_percentage:.1f}%)")
        
        # Verify memory optimization
        assert optimized_metrics.memory_average <= intensive_metrics.memory_average, "Optimized operation should use less or equal memory"
        assert optimized_metrics.success_rate >= intensive_metrics.success_rate, "Optimization should not reduce success rate"
        
        print("   ✅ Memory optimization validation completed")
