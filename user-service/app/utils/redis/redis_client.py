import redis
from typing import Optional
from app.core.config import settings

class RedisClient:
    """
    Simple helper class for Redis connection management.
    Provides methods to establish and close connections.
    """

    def __init__(self, host: str, port: int, db: int = 0, password: Optional[str] = None):
        """
        Initialize Redis client with connection parameters.

        Args:
            host: Redis server hostname or IP
            port: Redis server port
            db: Redis database number
            password: Redis password (None if not required)
        """
        self.host = host
        self.port = port
        self.db = db
        self.password = password
        self.connection_url = settings.REDIS_URI
        self._redis_client = None

    def connect(self) -> redis.Redis:
        """
        Establish a Redis connection.

        Returns:
            redis.Redis: Redis client instance
        """
        if self._redis_client is None:
            self._redis_client = redis.Redis.from_url(self.connection_url, decode_responses=True)
        return self._redis_client

    def close(self) -> None:
        """Close the Redis connection if it exists."""
        if self._redis_client is not None:
            self._redis_client.close()
            self._redis_client = None
