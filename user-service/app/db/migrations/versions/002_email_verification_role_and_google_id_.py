"""email_verification, role and google id addition

Revision ID: 002
Revises: 001
Create Date: 2025-03-18 17:32:16.535084

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
from enum import Enum


# Define the UserRole enum for Alembic
class UserRole(str, Enum):
    USER = "user"


# revision identifiers, used by Alembic.
revision = "002"
down_revision = "001"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create the enum type for UserRole
    userrole_enum = sa.Enum(UserRole, name="userrole")
    userrole_enum.create(op.get_bind(), checkfirst=True)

    # Add new columns to the users table
    op.add_column("users", sa.Column("google_id", sa.String(), nullable=True))
    op.add_column("users", sa.Column("role", userrole_enum, nullable=False, default=UserRole.USER))
    op.add_column("users", sa.Column("otp", sa.String(), nullable=True))
    op.add_column("users", sa.Column("otp_timestamp", sa.DateTime(), nullable=True))
    op.add_column("users", sa.Column("otp_attempts", sa.Integer(), nullable=True, default=0))
    op.add_column(
        "users", sa.Column("is_email_verified", sa.Boolean(), nullable=False, default=False)
    )

    # Add unique constraint for google_id
    op.create_unique_constraint("uq_users_google_id", "users", ["google_id"])

    # Update created_at and updated_at defaults to match the model
    op.alter_column(
        "users",
        "created_at",
        existing_type=sa.DateTime(),
        server_default=sa.func.now(),  # Matches datetime.utcnow in the model
        nullable=False,
    )
    op.alter_column(
        "users",
        "updated_at",
        existing_type=sa.DateTime(),
        server_default=sa.func.now(),  # Matches datetime.utcnow in the model
        nullable=False,
    )


def downgrade() -> None:
    # Remove the new columns
    op.drop_column("users", "is_email_verified")
    op.drop_column("users", "otp_attempts")
    op.drop_column("users", "otp_timestamp")
    op.drop_column("users", "otp")
    op.drop_column("users", "role")
    op.drop_column("users", "google_id")

    # Drop the unique constraint on google_id
    op.drop_constraint("uq_users_google_id", "users", type_="unique")

    # Revert created_at and updated_at to their original state
    op.alter_column(
        "users",
        "created_at",
        existing_type=sa.DateTime(),
        server_default=sa.text("now()"),
        nullable=False,
    )
    op.alter_column(
        "users",
        "updated_at",
        existing_type=sa.DateTime(),
        server_default=sa.text("now()"),
        nullable=False,
    )

    # Drop the userrole enum type
    sa.Enum(name="userrole").drop(op.get_bind(), checkfirst=True)
