from enum import Enum
import uuid
from datetime import datetime
from sqlalchemy import Column, ForeignKey, String, DateTime, JSON, Enum as SQLAlchemyEnum
from sqlalchemy.orm import relationship
from app.models.user import Base


class WaitlistStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    
class WaitlistEntry(Base):
    __tablename__ = "waitlist_entries"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String, unique=True, nullable=False, index=True)
    joined_at = Column(DateTime, default=datetime.utcnow)
    
    # --- New Status Field ---
    status = Column(SQLAlchemyEnum(WaitlistStatus), default=WaitlistStatus.PENDING, nullable=False)

    # Optional: Link back to the User if they eventually register
    # user_id = Column(String, ForeignKey("users.id"), nullable=True)
    # user = relationship("User", back_populates="waitlist_entry")

    def __repr__(self):
        return f"<WaitlistEntry {self.email}>"