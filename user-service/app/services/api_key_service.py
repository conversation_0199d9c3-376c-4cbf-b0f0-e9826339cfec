import uuid
import secrets
from datetime import datetime
import grpc
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.api_key import APIKey
from app.models.user import User
from app.grpc import user_pb2, user_pb2_grpc

class APIKeyService(user_pb2_grpc.UserServiceServicer):
    def get_db(self):
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()
            
    @staticmethod
    def generate_key_pair():
        """Generate a public/private key pair."""
        public_key = f"pk_{secrets.token_urlsafe(24)}"
        private_key = f"sk_{secrets.token_urlsafe(32)}"
        return public_key, private_key
    
    def generate_api_key(
        self, request: user_pb2.GenerateAPIKeyRequest, context: grpc.ServicerContext
    ) -> user_pb2.GenerateAPIKeyResponse:
        db = self.get_db()
        try:
            # Verify user exists
            user = db.query(User).filter(User.id == request.user_id).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.GenerateAPIKeyResponse(
                    success=False,
                    message="User not found"
                )

            public_key, private_key = self.generate_key_pair()

            api_key = APIKey(
                id=str(uuid.uuid4()),
                name=request.name,
                public_key=public_key,
                private_key=private_key,
                project=request.project if request.project else None,
                user_id=request.user_id  # Fixed the typo here
            )

            db.add(api_key)
            db.commit()
            db.refresh(api_key)

            return user_pb2.GenerateAPIKeyResponse(
                success=True,
                message="API key generated successfully",
                public_key=public_key,
                private_key=private_key
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to generate API key: {str(e)}")
            return user_pb2.GenerateAPIKeyResponse(
                success=False,
                message=f"Failed to generate API key: {str(e)}"  # Added more detailed error message
            )
        finally:
            db.close()
            

    def list_api_keys(
        self, request: user_pb2.ListAPIKeysRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListAPIKeysResponse:
        db = self.get_db()
        try:
            api_keys = db.query(APIKey).filter(APIKey.user_id == request.user_id).all()
            
            api_key_list = [
                user_pb2.APIKeyInfo(
                    id=key.id,
                    name=key.name,
                    public_key=key.public_key,
                    private_key=key.private_key,
                    project=key.project or "",
                    created_at=key.created_at.isoformat()
                ) for key in api_keys
            ]

            return user_pb2.ListAPIKeysResponse(
                success=True,
                message="API keys retrieved successfully",
                api_keys=api_key_list
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to list API keys: {str(e)}")
            return user_pb2.ListAPIKeysResponse(
                success=False,
                message="Internal server error"
            )
        finally:
            db.close()

    def get_api_key_by_id(
        self, request: user_pb2.GetAPIKeyByIdRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetAPIKeyByIdResponse:
        db = self.get_db()
        try:
            api_key = db.query(APIKey).filter(
                APIKey.id == request.api_key_id,
                APIKey.user_id == request.user_id
            ).first()

            if not api_key:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("API key not found")
                return user_pb2.GetAPIKeyByIdResponse(
                    success=False,
                    message="API key not found"
                )

            api_key_info = user_pb2.APIKeyInfo(
                id=api_key.id,
                name=api_key.name,
                public_key=api_key.public_key,
                private_key=api_key.private_key,
                project=api_key.project or "",
                created_at=api_key.created_at.isoformat()
            )

            return user_pb2.GetAPIKeyByIdResponse(
                success=True,
                message="API key retrieved successfully",
                api_key=api_key_info
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to retrieve API key: {str(e)}")
            return user_pb2.GetAPIKeyByIdResponse(
                success=False,
                message="Internal server error"
            )
        finally:
            db.close()

    def delete_api_key(
        self, request: user_pb2.DeleteAPIKeyRequest, context: grpc.ServicerContext
    ) -> user_pb2.DeleteAPIKeyResponse:
        db = self.get_db()
        try:
            api_key = db.query(APIKey).filter(
                APIKey.user_id == request.user_id,
                APIKey.id == request.key_id
            ).first()

            if not api_key:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("API key not found")
                return user_pb2.DeleteAPIKeyResponse(
                    success=False,
                    message="API key not found"
                )

            db.delete(api_key)
            db.commit()

            return user_pb2.DeleteAPIKeyResponse(
                success=True,
                message="API key deleted successfully"
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to delete API key: {str(e)}")
            return user_pb2.DeleteAPIKeyResponse(
                success=False,
                message="Internal server error"
            )
        finally:
            db.close()
