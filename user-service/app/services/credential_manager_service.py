import uuid
from datetime import datetime
import grpc
from app.db.session import <PERSON><PERSON><PERSON><PERSON>
from app.models.credential import Credential
from app.models.user import User
from app.grpc import user_pb2, user_pb2_grpc
import logging
from app.utils.secret_manager.secret_manager import EncryptionManager

class CredentialService(user_pb2_grpc.UserServiceServicer):
    def __init__(self):
        self.encryption_manager = EncryptionManager()
    
    def get_db(self):
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()
    
    def create_credential(
        self, request: user_pb2.CreateCredentialRequest, context: grpc.ServicerContext
    ) -> user_pb2.CreateCredentialResponse:
        db = self.get_db()
        try:
            # Verify user exists
            user = db.query(User).filter(User.id == request.owner_id).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.CreateCredentialResponse(
                    success=False,
                    message="User not found"
                )

            # Check if credential with same key_name already exists for this user
            existing_credential = db.query(Credential).filter(
                Credential.key_name == request.key_name,
                Credential.owner_id == request.owner_id
            ).first()
            
            if existing_credential:
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details("Credential with this key_name already exists")
                return user_pb2.CreateCredentialResponse(
                    success=False,
                    message="Credential with this key_name already exists"
                )

            # Create or get encryption key for user
            secret_id = self.encryption_manager._get_secret_name(request.owner_id)
            try:
                # Try to get existing key
                self.encryption_manager.get_user_encryption_key(request.owner_id)
                logging.info(f"Using existing encryption key for user: {request.owner_id}")
            except ValueError:
                # Create a new key if it doesn't exist
                logging.info(f"Creating new encryption key for user: {request.owner_id}")
                self.encryption_manager.create_and_store_user_key(secret_id)
            
            # Now encrypt the credential value
            encrypted_value = self.encryption_manager.encrypt(request.value, request.owner_id)

            credential = Credential(
                id=str(uuid.uuid4()),
                key_name=request.key_name,
                description=request.description if hasattr(request, "description") and request.description else None,
                value=encrypted_value,
                owner_id=request.owner_id
            )

            db.add(credential)
            db.commit()
            db.refresh(credential)

            return user_pb2.CreateCredentialResponse(
                success=True,
                message="Credential created successfully",
                id=credential.id,
                key_name=credential.key_name
            )

        except Exception as e:
            db.rollback()
            logging.error(f"Failed to create credential: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to create credential: {str(e)}")
            return user_pb2.CreateCredentialResponse(
                success=False,
                message=f"Failed to create credential: {str(e)}"
            )
        finally:
            db.close()
            
    def get_credential(
        self, request: user_pb2.GetCredentialRequest, context: grpc.ServicerContext
    ) -> user_pb2.GetCredentialResponse:
        db = self.get_db()
        try:
            # First verify user exists
            logging.info(f"Getting credential for user: {request.owner_id}")
            user = db.query(User).filter(User.id == request.owner_id).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.GetCredentialResponse(
                    success=False,
                    message="User not found"
                )
            
            # Then look for the credential
            credential = db.query(Credential).filter(
                Credential.id == request.credential_id,
                Credential.owner_id == request.owner_id
            ).first()
            
            if not credential:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Credential not found")
                return user_pb2.GetCredentialResponse(
                    success=False,
                    message="Credential not found"
                )
            
            # Decrypt the credential value
            try:
                decrypted_value = self.encryption_manager.decrypt(credential.value, request.owner_id)
            except Exception as e:
                logging.error(f"Failed to decrypt credential: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Failed to decrypt credential")
                return user_pb2.GetCredentialResponse(
                    success=False,
                    message="Failed to decrypt credential"
                )

            credential.last_used_at = datetime.utcnow()
            
            db.commit()
            credential_info = user_pb2.CredentialInfo(
                id=credential.id,
                key_name=credential.key_name,
                description=credential.description if credential.description else "",
                value=credential.value,  # Use decrypted value here
                created_at=credential.created_at.isoformat(),
                last_used_at=credential.last_used_at.isoformat(),
                updated_at=credential.updated_at.isoformat() if credential.updated_at else credential.created_at.isoformat()
            )

            return user_pb2.GetCredentialResponse(
                success=True,
                message="Credential retrieved successfully",
                credential=credential_info
            )
        except Exception as e:
            logging.error(f"Failed to get credential: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to get credential: {str(e)}")
            return user_pb2.GetCredentialResponse(
                success=False,
                message=f"Internal server error"
            )
        finally:
            db.close()
            
    def list_credentials(
        self, request: user_pb2.ListCredentialsRequest, context: grpc.ServicerContext
    ) -> user_pb2.ListCredentialsResponse:
        db = self.get_db()
        try:
            logging.info(f"Listing credentials for user: {request.owner_id}")
            # First verify user exists
            user = db.query(User).filter(User.id == request.owner_id).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.ListCredentialsResponse(
                    success=False,
                    message="User not found"
                )

            # Get credentials - empty list is valid result
            credentials = db.query(Credential).filter(Credential.owner_id == request.owner_id).all()
            logging.info(f"Found {len(credentials)} credentials for user")

            # Even if no credentials found, return success with empty list
            credential_list = []
            for cred in credentials:
                try:
                    # Decrypt each credential value
                    decrypted_value = self.encryption_manager.decrypt(cred.value, request.owner_id)
                    
                    credential_list.append(user_pb2.CredentialInfo(
                        id=cred.id,
                        key_name=cred.key_name,
                        description=cred.description if cred.description else "",
                        value=cred.value,  # Use decrypted value
                        created_at=cred.created_at.isoformat(),
                        last_used_at=cred.last_used_at.isoformat() if cred.last_used_at else cred.created_at.isoformat(),
                        updated_at=cred.updated_at.isoformat() if cred.updated_at else cred.created_at.isoformat()
                    ))
                except Exception as e:
                    logging.error(f"Failed to decrypt credential {cred.id}: {str(e)}")
                    # Skip this credential if decryption fails
                    continue
            
            return user_pb2.ListCredentialsResponse(
                success=True,
                message="Credentials retrieved successfully" if credential_list else "No credentials found",
                credentials=credential_list
            )
        except Exception as e:
            logging.error(f"Failed to list credentials: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to list credentials: {str(e)}")
            return user_pb2.ListCredentialsResponse(
                success=False,
                message="Internal server error"
            )
        finally:
            db.close()
            
    def delete_credential(
        self, request: user_pb2.DeleteCredentialRequest, context: grpc.ServicerContext
    ) -> user_pb2.DeleteCredentialResponse:
        db = self.get_db()
        try:
            # First verify user exists
            user = db.query(User).filter(User.id == request.owner_id).first()
            if not user:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User not found")
                return user_pb2.DeleteCredentialResponse(
                    success=False,
                    message="User not found"
                )
                
            credential = db.query(Credential).filter(
                Credential.id == request.credential_id,
                Credential.owner_id == request.owner_id
            ).first()
            
            if not credential:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Credential not found")
                return user_pb2.DeleteCredentialResponse(
                    success=False,
                    message="Credential not found"
                )
            
            # Delete the credential
            db.delete(credential)
            db.commit()
            
            return user_pb2.DeleteCredentialResponse(
                success=True,
                message="Credential deleted successfully"
            )
        except Exception as e:
            db.rollback()
            logging.error(f"Failed to delete credential: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to delete credential: {str(e)}")
            return user_pb2.DeleteCredentialResponse(
                success=False,
                message="Internal server error"
            )
        finally:
            db.close()

    def update_credential(
        self, request: user_pb2.UpdateCredentialRequest, context: grpc.ServicerContext
    ) -> user_pb2.UpdateCredentialResponse:
        db = self.get_db()
        try:
            # Find the credential
            credential = db.query(Credential).filter(
                Credential.id == request.credential_id,
                Credential.owner_id == request.owner_id
            ).first()
            
            if not credential:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Credential not found")
                return user_pb2.UpdateCredentialResponse(
                    success=False,
                    message="Credential not found"
                )
            
            # Update fields if provided
            if hasattr(request, 'key_name') and request.key_name:
                credential.key_name = request.key_name
            
            if hasattr(request, 'value') and request.value:
                # Encrypt the new value
                encrypted_value = self.encryption_manager.encrypt(request.value, request.owner_id)
                credential.value = encrypted_value
            
            if hasattr(request, 'description'):
                credential.description = request.description
            
            # Update the updated_at timestamp
            credential.updated_at = datetime.utcnow()
            
            db.commit()
            print(f"Updated credential: {credential}")
            return user_pb2.UpdateCredentialResponse(
                success=True,
                message="Credential updated successfully",
                id=credential.id,
                key_name=credential.key_name
            )
        except Exception as e:
            db.rollback()
            print(f"Error updating credential: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error updating credential: {str(e)}")
            logging.error(f"Error updating credential: {str(e)}")
            return user_pb2.UpdateCredentialResponse(
                success=False,
                message=f"Error updating credential: {str(e)}"
            )
        finally:
            db.close()
