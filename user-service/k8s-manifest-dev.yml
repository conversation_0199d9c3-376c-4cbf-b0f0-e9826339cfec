apiVersion: v1
kind: ServiceAccount
metadata:
  name: user-service-ai-sa
  namespace: ruh-common-dev
  labels:
    name: user-service-ai-sa
    namespace: ruh-common-dev
    app: user-service-ai
    deployment: user-service-ai-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: user-service-ai-dp
  namespace: ruh-common-dev
  labels:
    name: user-service-ai-dp
    namespace: ruh-common-dev
    app: user-service-ai
    serviceaccount: user-service-ai-sa
    deployment: user-service-ai-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: user-service-ai
      deployment: user-service-ai-dp
  template:
    metadata:
      labels:
        namespace: ruh-common-dev
        app: user-service-ai
        deployment: user-service-ai-dp
    spec:
      serviceAccountName: user-service-ai-sa      
      containers:
      - name: user-service-ai
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50052
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: user-service-ai-svc
  namespace: ruh-common-dev
spec:
  selector:
    app: user-service-ai
    deployment: user-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50052
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name:user-service-user-hpa
#   namespace:ruh-common-dev
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name:user-service-user-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: user-service-user-ingress
  namespace: ruh-common-dev
spec:
  ingressClassName: nginx
  rules:
  - host: user-service-dev.ruh.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: user-service-ai-svc
            port:
              number: 80




